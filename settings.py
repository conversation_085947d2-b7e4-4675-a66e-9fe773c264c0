import logging
from public_utils_configs.util.logger import ConcurrentTimedRotatingFileHandler
ConcurrentTimedRotatingFileHandler.init_logger(level="INFO")
logging.getLogger(__name__)

COUNTRY_NAME_MAP = {
            'Hong Kong'.lower(): 'Hong Kong(China)'.lower(),
            'Hong Kong SAR'.lower(): 'Hong Kong(China)'.lower(),
            'Taiwan'.lower(): 'Taiwan(China)'.lower(),
            'Macao'.lower(): 'Macao(China)'.lower(),
        }

PROVINCE_NAME_MAP = {
    'Kowloon'.lower(): 'Kowloon City'.lower(),
    'Abu Dhabi'.lower(): 'Abu Dhabi Emirate'.lower(),
}

CITY_NAME_MAP = {}

# ES 配置
# ES_NODES = ['http://10.1.1.11:9200']
# ES_USER = 'elastic'
# ES_PWD = 'Upkuajing@2022'

ES_NODES = ['http://10.1.1.40:9200']
ES_USER = 'elastic'
ES_PWD = 'Uptook@2024'





