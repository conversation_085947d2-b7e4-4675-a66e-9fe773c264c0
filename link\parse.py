# 页面解析
from link.parse_search_page import SearchAllParse
from link.parse_human_info_page import NotLoginHumanInfoParse, LoginHumanParse
from link.parse_company_info_page import NotLoginCompanyParse, LoginCompanyParse


class LinkParser:
    """
    解析入口
    """
    # 综合搜索页解析
    search_all_parse_obj = SearchAllParse()
    # 未登录人物详情解析
    not_login_human_info_parse_obj = NotLoginHumanInfoParse()
    # 登录人物页详情解析
    login_human_info_parse_obj = LoginHumanParse()
    # 未登录页公司详情解析
    not_login_company_parse_obj = NotLoginCompanyParse()
    # 登录公司详情解析
    login_company_parse_obj = LoginCompanyParse()


def test_unit():
    with open('../test/test.html', 'r', encoding='utf-8') as f:
        html_str = f.read()
    human_data = NotLoginCompanyParse().parse_data(html_str)
    print(human_data)


if __name__ == '__main__':
    test_unit()
