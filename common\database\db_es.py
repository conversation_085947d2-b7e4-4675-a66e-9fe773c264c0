#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: db_es.py
Description: es常用封装
Author:
Email:
Date:
"""
import time
import asyncio
import sys
import requests
import json
from requests.models import Response
from typing import Optional, List, Dict
from settings import logging


class ElasticSearch:
    def __init__(self, user, passwd):
        self.auth = (user, passwd)

    def bulk_import_to_es(
            self,
            url: str,
            datalist: List[Dict],
            uniq_col_name: str,
            headers: dict = None,
            retry: int = 3
    ) -> Optional[Response]:
        """
        :param url: 执行接口
        :param datalist: 数据列表
        :param uniq_col_name: 数据的唯一标识字段名称
        :param headers: 请求头
        :param retry: 重试次数
        :return:
        """
        if not headers:
            headers = {'Content-Type': 'application/json'}

        # 准备 Bulk API 请求的数据
        bulk_data = []
        for record in datalist:
            if uniq_col_name not in record or not record[uniq_col_name]:
                continue
            # 每条数据需要两部分: 操作指令和数据
            # 操作指令
            action = {
                "index": {
                    "_id": record[uniq_col_name]  # 使用 uuid 作为 Elasticsearch 的文档 ID
                }
            }
            # 数据本身
            bulk_data.append(action)
            bulk_data.append(record)

        # 将数据转换为 Elasticsearch Bulk API 所需的格式
        bulk_data_str = '\n'.join([json.dumps(d) for d in bulk_data]) + '\n'

        return self.request_bulk(url, bulk_data_str, headers, retry)

    def request_bulk(self, url: str, data: str, headers: dict = None, retry: int = 3, timeout=180) -> Optional[Response]:
        """
        更新、插入的执行入口
        """
        if not headers:
            headers = {'Content-Type': 'application/json'}
        for _ in range(retry):
            try:
                response = requests.post(url, headers=headers, data=data, auth=self.auth, timeout=timeout)
                if response.status_code == 200:
                    return response
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logging.warning(f"{exc_type.__name__}: {e}")
        logging.error({"msg": "最大重试次数", "data": f'{data}'[:10]})
        return None

    def request(self, method, url: str, body: Optional[dict] = None, retry: int = 3, timeout=180) -> Optional[Response]:
        for _ in range(retry):
            try:
                response = requests.request(method, url, auth=self.auth, json=body, timeout=timeout)
                response.raise_for_status()
                return response
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logging.warning(f"{exc_type.__name__}: {e}")
        logging.error({"msg": "最大重试次数", "body": body})

    async def request_async(self, method, url: str, body: Optional[dict] = None, retry: int = 3) -> Optional[Response]:
        s_t = time.time()
        res = await asyncio.to_thread(self.request, method, url, body, retry)
        e_t = time.time()
        res_data = res.json()
        res_data['es_hs'] = "{:.4f}".format(e_t - s_t)
        return res_data
