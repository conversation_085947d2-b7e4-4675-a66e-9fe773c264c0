#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_funcs.py
@Date: 2023/12/22
@Desc: 
@Server: 
"""
import logging
import re
from urllib.parse import urlparse


class PublicFuncs:
    def __init__(self):
        # 替换公司后缀
        self.regex_replace = [
            (re.compile(r'\bLimited Liability Company\b', re.IGNORECASE), 'LLC'),  # Limited Liability Company
            (re.compile(r'\bLimited Liability Partnership\b', re.IGNORECASE), 'LLP'),  # Limited Liability Partnership
            (re.compile(r'\bPublic Limited Company\b', re.IGNORECASE), 'PLC'),  # Public Limited Company
            (re.compile(r'\bCompany\b', re.IGNORECASE), 'Co'),  # Company
            (re.compile(r'\bLimited\b', re.IGNORECASE), 'Ltd'),  # Limited
            (re.compile(r'\bIncorporated\b', re.IGNORECASE), 'Inc'),  # Incorporated
            # 添加其他需要替换的后缀
        ]
        # 移除公司后缀
        self.regex_remove = re.compile(r'\b(Co\.?|Company|Ltd\.?|Limited|Inc\.?|Incorporated|LLC|LLP|PLC)\b',
                                       re.IGNORECASE)
        # 清理公司名
        self.regex_clean_cname = re.compile(r'[,.“]+$', re.IGNORECASE)
        # 电话仅保留数字
        self.regex_retain_numbers = re.compile(r'[^0-9]', re.IGNORECASE)
        # 电话保留数字、空格和加号
        self.regex_transform_number = re.compile(r'[^\d +]', re.IGNORECASE)

    def clean_company_name(self, company_name: str):
        """
        清洗公司名称
        """
        if not company_name:
            return ''
        company_name = company_name.lower()
        # 去除末尾为逗号、点号字符
        company_name = self.regex_clean_cname.sub('', company_name)
        return company_name

    def replace_company_suffix(self, company_name: str) -> str:
        """
        替换公司后缀
        :param company_name: 公司名
        :return:
        """
        if not company_name:
            return ''
        # 替换后缀
        for regex, replacement in self.regex_replace:
            company_name = regex.sub(replacement, company_name).strip()
        return company_name

    def remove_company_suffix(self, company_name: str) -> str:
        """
        移除公司后缀
        :param company_name: 公司名
        :return:
        """
        if not company_name:
            return ''
        return self.regex_remove.sub('', company_name)

    def retain_numbers(self, number):
        """
        仅保留数字
        """
        num_str = self.regex_retain_numbers.sub('', number)
        return num_str

    def transform_number(self, number):
        number = self.regex_transform_number.sub('', number)
        number = number.strip()
        return number

    def clean_website(self, website: str):
        """
        获取 网址 域名
        """
        if not website:
            return ''
        try:
            anlyse = urlparse(website)
            domain = anlyse.hostname
        except Exception as e:
            domain = None
        if not domain:
            domain = website.split('?')[0].split('/')[0].replace('https', '').replace('http', '').replace('://', '')
        domain = domain.lower()
        return domain

    @staticmethod
    def check_duplicate_cols(data: dict, duplicate_cols: list) -> bool:
        """
        校检约束字段是否都存在值
        :param data:
        :param duplicate_cols:
        :return:
        """
        for duplicate_col in duplicate_cols:
            d_val = data.get(duplicate_col, '')
            if not d_val and d_val != 0:
                return False
        return True

    @staticmethod
    def mate_area_data(area_map: dict, country_code: str = None, country: str = None, province: str = None, city: str = None) -> dict:
        """
        处理国家、省份、城市信息
        :param area_map: 映射字典
        :param country_code: 国家二字码
        :param country: 国家
        :param province: 省份
        :param city: 城市
        :return: 数据字典
        """
        # logging.debug({"country_code": country_code, "country": country, "province": province, "city": city})
        # 初始化
        country_en = ''
        country_cn = ''
        country_iso_code = ''
        province_en = ''
        province_cn = ''
        province_id = 0
        code_from_province = ''
        city_en = ''
        city_cn = ''
        city_id = 0
        province_id_from_city = 0
        country_code_from_city = ''
        # 预处理
        country_code = country_code.lower().strip() if country_code else ''
        country = country.lower().strip() if country else ''
        province = province.lower().strip() if province else ''
        city = city.lower().strip() if city else ''
        # 匹配国家信息
        # 优先使用二字码
        country_info = area_map.get('country_map').get(country_code) or area_map.get('country_map').get(country)
        if isinstance(country_info, dict):
            country_en = country_info.get('en', '')
            country_cn = country_info.get('cn', '')
            country_iso_code = country_info.get('code', '')
        # 匹配省份信息
        province_info = area_map.get('province_map').get(province)
        if isinstance(province_info, dict):
            province_en = province_info.get('en', '')
            province_cn = province_info.get('cn', '')
            province_id = province_info.get('id', 0)
            code_from_province = province_info.get('code', '')
        # 匹配城市信息
        city_info = area_map.get('city_map').get(city)
        if isinstance(city_info, dict):
            city_en = city_info.get('en', '')
            city_cn = city_info.get('cn', '')
            city_id = city_info.get('id', 0)
            province_id_from_city = city_info.get('province_id', 0)
            country_code_from_city = city_info.get('code', '')
        # 冲突的情况
        if country_iso_code != '':
            # 国家和省份冲突
            if country_iso_code.lower() != code_from_province.lower():
                province_id = 0
            # 国家和城市冲突
            elif country_iso_code.lower() != country_code_from_city.lower():
                city_id = 0
        # 省份和城市冲突
        if province_id != 0 and province_id != province_id_from_city:
            city_id = 0
        # 缺省份，不缺城市的情况
        if province_id == 0 and city_id != 0 and province_id_from_city != 0:
            _province_map_cache = area_map.get('province_map', dict()).get(province_id_from_city, dict())
            province_id = _province_map_cache.get('id', '')
            province_en = _province_map_cache.get('en', '')
            province_cn = _province_map_cache.get('cn', '')
            code_from_province = _province_map_cache.get('code', '')
        # 缺国家，不缺省份的情况
        if country_iso_code == '' and province_id != 0 and code_from_province != '':
            _country_map_cache = area_map.get('country_map', dict()).get(code_from_province.lower(), dict())
            country_iso_code = _country_map_cache.get('code', '')
            country_en = _country_map_cache.get('en', '')
            country_cn = _country_map_cache.get('cn', '')
        # return
        area_data = {
            "country_en": country_en, "country_cn": country_cn, "country_iso_code": country_iso_code,
            "province_en": province_en, "province_cn": province_cn, "province_id": province_id,
            "city_en": city_en, "city_cn": city_cn, "city_id": city_id
        }
        # print(area_data)
        return area_data

    @staticmethod
    def mate_industry_id(industry_map: dict, industry_id: int = None, industry: str = None,
                         industry_class: str = None, industry_code: str = None) -> int:
        """
        匹配行业ID
        :param industry_map: 行业映射数据
        {industry_class: {industry_code: {'industry_id': industry_id, 'industry_en': industry_en, 'industry_cn': industry_cn}}}
        :param industry_id: 行业表ID
        :param industry: 行业
        :param industry_class: 行业类别
        :param industry_code: 行业代码
        :return: industry_id; 行业ID
        """
        if industry_id:
            return industry_id
        elif industry:
            # TODO: 行业文本的相似性匹配待实现
            for _industry_class, _industry_code_data in industry_map.items():
                for _industry_code, _industry_data in _industry_code_data.items():
                    _industry_id = _industry_data.get('industry_id', 0)
                    _industry_en = _industry_data.get('industry_en', '')
                    _industry_cn = _industry_data.get('industry_cn', '')
                    if industry.lower() in [_industry_en.lower(), _industry_cn.lower()]:
                        return _industry_id
        elif industry_class and industry_code:
            _industry_data = industry_map.get(industry_class, {}).get(industry_code, {})
            _industry_id = _industry_data.get('industry_id', 0)
            return _industry_id
        else:
            return 0

    def clean_company_size(self, employee: int):
        """
        清洗公司规模
        :param employee: 员工人数
        """
        if not employee:
            employee = 0
        try:
            employee = int(employee) if employee else 0
        except Exception:
            employee = 0
        if employee <= 0:
            company_size = ''
        elif employee > 0 and employee <= 10:
            company_size = '0-10'
        elif employee > 10 and employee <= 50:
            company_size = '11-50'
        elif employee > 50 and employee <= 200:
            company_size = '51-200'
        elif employee > 200 and employee <= 500:
            company_size = '201-500'
        elif employee > 500 and employee <= 1000:
            company_size = '501-1000'
        elif employee > 1000 and employee <= 5000:
            company_size = '1001-5000'
        elif employee > 5000 and employee <= 10000:
            company_size = '5001-10000'
        else:
            company_size = '10001+'
        return company_size

    def test_unit(self):
        # 单元测试
        company_name = 'vong sawat textile co ltd..,'
        company_name = self.clean_company_name(company_name)
        print(company_name)


public_funcs_obj = PublicFuncs()
if __name__ == '__main__':
    public_funcs_obj.test_unit()
