import hashlib
from pydantic import BaseModel, constr, model_validator
from django.db import models


class LinkCompanyBaseInfoId(BaseModel):
    """
    领英 公司数据 模型
    """
    job_company_id: constr(max_length=200, min_length=1)  # 领英公司ID(领英公司链接ID)    &-son
    job_company_name: constr(max_length=300, min_length=1)  # 公司名称    & son
    job_company_website: constr(max_length=200) = ''  # 公司网址
    job_company_size: constr(max_length=100) = '1-10'  # 公司规模   1-10;11-50;51-200;201-500;501-1000;1001-5000;5001-10000;10001+
    job_company_founded: constr(max_length=50) = ''  # 公司成立年份    2010
    job_company_industry: constr(max_length=100) = ''  # 行业
    job_company_linkedin_url: constr(max_length=255) = ''  # 公司领英链接  linkedin.com/company/&-son
    job_company_linkedin_id: constr(max_length=255) = ''  # 公司领英ID
    job_company_facebook_url: constr(max_length=255) = ''  # 公司facebook链接    facebook.com/andonelab
    job_company_twitter_url: constr(max_length=255) = ''  # 公司twitter链接    twitter.com/n_beyond
    job_company_location_name: constr(max_length=255) = ''  # 公司地址信息     cedarhurst, new york, united states
    job_company_location_locality: constr(max_length=255) = ''  # 公司所在市区     Cedarhurst
    job_company_location_metro: constr(max_length=255) = ''  # 公司所在位置     new york, new york
    job_company_location_region: constr(max_length=255) = ''  # 公司所在州省     New York
    job_company_location_geo: constr(max_length=255) = ''  # 公司所在坐标     40.62,-73.72
    job_company_location_street_address: constr(max_length=255) = ''  # 街道1     172 foundling court
    job_company_location_address_line_2: constr(max_length=255) = ''  # 街道2     The Brunswick Centre, Marchmont Street
    job_company_location_postal_code: constr(max_length=255) = ''  # 邮编     WC1N 1QE
    job_company_location_country: constr(max_length=255) = ''  # 公司所在国家     United Kingdom
    job_company_location_continent: constr(max_length=255) = ''  # 公司所在洲     europe
    specialities: constr(max_length=1024) = ''  # 关于-领域
    description: str = ''  # 关于-概览
    logo_url: constr(max_length=255) = ''  # 公司logo
    country_ios_code: constr(max_length=4) = ''  # 国家代码
    job_search_url: constr(max_length=1000) = ''  # 职位发布链接   https://www.linkedin.com/jobs/search?geoid=92000000&f_c=12806583
    phone: constr(max_length=255) = ''  # 电话
    email: constr(max_length=255) = ''  # 邮箱


class LinkPersonList(BaseModel):
    """
    领英 人物列表 模型
    """
    full_name: constr(max_length=300, min_length=1)  # 领英人物 全名
    linkedin_username: constr(max_length=300, min_length=1)  # 领英标识(领英人物链接ID); anders-olauson-4067b746
    lid: constr(max_length=30) = ''  # 领英人物ID
    gender: constr(max_length=10) = ''  # 领英人物 性别; male-男;female-女
    birth_year: constr(max_length=10) = ''  # 出生年
    birth_date: constr(max_length=20) = ''  # 出生日期
    linkedin_username_clean: constr(max_length=300) = ''  # 领英标识(清洗后); anders-olauson-4067b746
    facebook_username: constr(max_length=250) = ''  # facebook标识; anders.olauson
    twitter_username: constr(max_length=100) = ''  # twitter标识; gratiaswines
    github_username: constr(max_length=250) = ''  # github标识
    work_email: constr(max_length=200) = ''  # 工作邮箱
    mobile_phone: constr(max_length=100) = ''  # 移动电话
    industry: constr(max_length=200) = ''  # 行业
    job_title: constr(max_length=500) = ''  # 职称
    job_title_role: constr(max_length=100) = ''  # 职务角色
    job_title_sub_role: constr(max_length=100) = ''  # 职务子角色
    job_title_levels: constr(max_length=100) = ''  # 职务级别
    job_company_id: constr(max_length=200) = ''  # 领英公司ID
    job_company_name: constr(max_length=300) = ''  # 公司名称
    job_last_updated: constr(max_length=20) = ''  # 职位最后更新时间
    job_start_date: constr(max_length=100) = ''  # 职位开始时间
    location_name: constr(max_length=255) = ''  # 人物地址信息    london, london, united kingdom
    location_locality: constr(max_length=255) = ''  # 人物所在市区    london
    location_region: constr(max_length=255) = ''  # 人物所在州省    london
    location_country: constr(max_length=255) = ''  # 人物所在国家    united kingdom
    country_ios_code: constr(max_length=4) = ''  # 国家代码    GB
    inferred_years_experience: constr(max_length=255) = ''  # 推断年经验
    summary: str = ''  # 总结
    phone_numbers: constr(max_length=255) = ''  # 电话    ['+447909756097']
    emails: str = ''  # 邮箱    [{"address": "<EMAIL>", "type": "personal"}, {"address": "<EMAIL>", "type": "personal"}]
    interests: str = ''     # 兴趣爱好  ['books', 'frisbee']
    skills: str = ''     # 技能  ['microsoft office', 'customer service', 'microsoft word', 'research', 'microsoft powerpoint']
    street_addresses: str = ''     # 街道
    profiles: str = ''     # 简介
    certifications: str = ''     # 证书
    languages: str = ''     # 语言    [{'name': 'english', 'proficiency': ''}, {'name': 'russian', 'proficiency': ''}, {'name': 'ukrainian', 'proficiency': ''}]

    @model_validator(mode='before')
    def _create_lid(cls, values):
        """
        创建 领英人物 ID
        """
        lid = values.get('lid', '')
        if not lid:
            linkedin_username = values.get('linkedin_username', '')
            lid = hashlib.md5(linkedin_username.encode()).hexdigest()[:22] + '_0000'
            values['lid'] = lid
        return values

    @model_validator(mode='before')
    def clen_linkedin_username(cls, values):
        """
        获取 linkedin_username_clean
        """
        linkedin_username = values.get('linkedin_username', '')
        linkedin_username_clean = values.get('linkedin_username_clean', '')
        if not linkedin_username_clean:
            values['linkedin_username_clean'] = linkedin_username
        return values


class LinkPersonLogo(BaseModel):
    """
    领英 人物头像 模型
    """
    lid: constr(max_length=30, min_length=1)  # 领英人物ID
    logo_url: constr(max_length=500, min_length=1)  # 图片原链接   https://media.licdn.com/dms/image/C4D03AQFCxbbb8TPsSg/profile-displayphoto-shrink_100_100/0/1632652333146?e=1693440000&v=beta&t=1G61b7OACUQAereh741G0rHa5R3gxLuU3hwqGACBx9g
    logo_url_md5: constr(max_length=500) = ''  # 图片原链接MD5

    @model_validator(mode='before')
    def create_logo_url_md5(cls, values):
        """
        获取 logo_url_md5
        """
        logo_url_md5 = values.get('logo_url_md5', '')
        logo_url = values.get('logo_url', '')
        if not logo_url_md5 and logo_url:
            imd5 = hashlib.md5(logo_url.encode()).hexdigest()
            values['logo_url_md5'] = imd5
        return values


class LinkCompanyLogo(BaseModel):
    """
    领英 公司头像 模型
    """
    job_company_id: constr(max_length=200, min_length=1)  # 领英公司 ID
    logo_url: constr(max_length=500, min_length=1)  # 图片原链接   https://media.licdn.com/dms/image/C4D03AQFCxbbb8TPsSg/profile-displayphoto-shrink_100_100/0/1632652333146?e=1693440000&v=beta&t=1G61b7OACUQAereh741G0rHa5R3gxLuU3hwqGACBx9g
    logo_url_md5: constr(max_length=500) = ''  # 图片原链接MD5

    @model_validator(mode='before')
    def create_logo_url_md5(cls, values):
        """
        获取 logo_url_md5
        """
        logo_url_md5 = values.get('logo_url_md5', '')
        logo_url = values.get('logo_url', '')
        if not logo_url_md5 and logo_url:
            imd5 = hashlib.md5(logo_url.encode()).hexdigest()
            values['logo_url_md5'] = imd5
        return values


class LinkPersonEducation(BaseModel):
    """
    领英 人物教育经历 模型
    """
    lid: constr(max_length=30, min_length=1)  # 领英人物ID
    full_name: constr(max_length=300, min_length=1)  # 领英人物 全名
    school_name: constr(max_length=500, min_length=1)  # 学校名称
    linkedin_url: constr(max_length=500) = ''  # 领英链接  linkedin.com/school/lehigh-university
    sid: constr(max_length=50) = ''  # 学校id
    school_type: constr(max_length=1000) = ''  # 学校类型
    country_iso_code: constr(max_length=4) = ''  # 国家代码
    location_country: constr(max_length=255) = ''  # 所属国家
    location_region: constr(max_length=255) = ''  # 所属区域
    location_locality: constr(max_length=255) = ''  # 所属地点
    location_continent: constr(max_length=255) = ''  # 所属大陆
    linkedin_id: constr(max_length=100) = ''  # 领英链接ID  19276
    facebook_url: constr(max_length=500) = ''  # facebook链接  facebook.com/lehighu
    twitter_url: constr(max_length=500) = ''  # twitter链接  twitter.com/lehighu
    website: constr(max_length=255) = ''  # 网址  lehigh.edu
    domain: constr(max_length=255) = ''  # 域名  lehigh.edu
    start_date: constr(max_length=20) = ''  # 开始时间  1982
    end_date: constr(max_length=20) = ''  # 截止时间  1984
    degrees: str = ''  # 学业程度  ["master of business administration", "masters"]
    majors: str = ''  # 专业  ["finance"]
    minors: str = ''  # 辅修科目  ["business"]
    gpa: constr(max_length=255) = ''  # 学分
    summary: str = ''  # 总结

    @model_validator(mode='before')
    def _create_sid(cls, values):
        """
        创建 领英人物 ID
        """
        sid = values.get('sid', '')
        if not sid:
            school_name = values.get('school_name', '')
            sid = hashlib.md5(school_name.encode()).hexdigest()
            values['sid'] = sid
        return values


class LinkPersonExperience(BaseModel):
    """
    领英 人物任职经历 模型
    """
    lid: constr(max_length=30, min_length=1)  # 领英人物ID
    full_name: constr(max_length=300, min_length=1)  # 领英人物 全名
    job_company_id: constr(max_length=200, min_length=1)  # 领英公司ID
    job_company_name: constr(max_length=300, min_length=1)  # 公司名
    job_company_website: constr(max_length=200) = ''  # 公司网址
    job_company_size: constr(max_length=100) = ''  # 公司规模   1-10;11-50;51-200;201-500;501-1000;1001-5000;5001-10000;10001+
    job_company_founded: constr(max_length=50) = ''  # 公司成立年份    2010
    job_company_industry: constr(max_length=100) = ''  # 行业
    job_company_linkedin_url: constr(max_length=255) = ''  # 公司领英链接  linkedin.com/company/&-son
    job_company_linkedin_id: constr(max_length=255) = ''  # 公司领英ID
    job_company_facebook_url: constr(max_length=255) = ''  # 公司facebook链接    facebook.com/andonelab
    job_company_twitter_url: constr(max_length=255) = ''  # 公司twitter链接    twitter.com/n_beyond
    job_company_location_name: constr(max_length=255) = ''  # 公司地址信息     cedarhurst, new york, united states
    job_company_location_locality: constr(max_length=255) = ''  # 公司所在市区     Cedarhurst
    job_company_location_metro: constr(max_length=255) = ''  # 公司所在位置     new york, new york
    job_company_location_region: constr(max_length=255) = ''  # 公司所在州省     New York
    job_company_location_geo: constr(max_length=255) = ''  # 公司所在坐标     40.62,-73.72
    job_company_location_street_address: constr(max_length=255) = ''  # 街道1     172 foundling court
    job_company_location_address_line_2: constr(max_length=255) = ''  # 街道2     The Brunswick Centre, Marchmont Street
    job_company_location_postal_code: constr(max_length=255) = ''  # 邮编     WC1N 1QE
    job_company_location_country: constr(max_length=255) = ''  # 公司所在国家     United Kingdom
    job_company_location_continent: constr(max_length=255) = ''  # 公司所在洲     europe
    specialities: constr(max_length=1024) = ''  # 关于-领域
    description: str = ''  # 关于-概览
    logo_url: constr(max_length=255) = ''  # 公司logo
    country_ios_code: constr(max_length=4) = ''  # 国家代码
    start_date: constr(max_length=20) = ''  # 开始时间  2008-09
    end_date: constr(max_length=20) = ''  # 截止时间  2010-09
    title_name: constr(max_length=1000) = ''  # 担任职位
    title_role: constr(max_length=500) = ''  # 担任角色
    title_sub_role: constr(max_length=500) = ''  # 担任子角色
    title_levels: constr(max_length=500) = ''  # 职级 ["manager", "senior"]
    summary: str = ''  # 经历总结
    is_primary: constr(max_length=20) = ''  # 是否是主要的; true; false
