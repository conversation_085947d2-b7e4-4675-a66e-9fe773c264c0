from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult
from _models.company_gz.db_link import LinkCompanyBaseInfoId
from typing import List, Union


class TBLinekdinCompanyBaseInfoId:
    db = 'db_link'
    table = 'linekdin_company_base_info_id'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql
        self.field_schema = self.db_mysql.read_column_default(self.table, self.db)

    def save_data(self, data: List[LinkCompanyBaseInfoId], is_safe=True) -> Union[None, MysqlResult]:
        """
        存储更新数据
        """
        if not data:
            return
        if is_safe:
            field_schema = self.field_schema
        else:
            field_schema = None
        datalist = [d.model_dump() for d in data]
        res = self.db_mysql.save(self.table, datalist, db=self.db, field_schema=field_schema)
        return res

    def search_data_by_job_company_id(self, job_company_ids: list, fields: list = None) -> Union[None, MysqlResult]:
        """
        通过 领英 链接 ID 查找数据
        """
        if not job_company_ids:
            return None
        if not fields:
            fields = ["id", "job_company_id"]
        cols = ','.join(fields)
        bfhs = ','.join(['%s'] * len(job_company_ids))
        sql = f'''
            select {cols} from {self.db}.{self.table} 
            where job_company_id in ({bfhs})
        '''
        value = job_company_ids
        res = self.db_mysql.execute(sql, value=value)
        return res

    def search_data_by_job_company_name_and_country_code(self, job_company_names_and_country: list, fields: list = None) -> Union[None, MysqlResult]:
        """
        通过job_company_name和job_company_location_country去匹配job_company_id
        """
        if not job_company_names_and_country:
            return None
        if not fields:
            fields = ["id", "job_company_id"]
        cols = ','.join(fields)
        bfhs = ','.join(['(%s,%s)'] * len(job_company_names_and_country))

        sql = f'''
            select {cols} from {self.db}.{self.table} 
            where (job_company_name,country_ios_code) in ({bfhs})
        '''
        value = job_company_names_and_country
        # 展平参数列表
        values = [item for pair in value for item in pair]
        res = self.db_mysql.execute(sql, value=values)
        return res

    def search_job_company_id_by_job_company_linkedin_id(self, job_company_ids: list, fields: list = None) -> Union[None, MysqlResult]:
        if not job_company_ids:
            return None
        if not fields:
            fields = ["id", "job_company_linkedin_id", "job_company_id"]
        cols = ','.join(fields)
        bfhs = ','.join(['%s'] * len(job_company_ids))
        sql = f'''
            select {cols} from {self.db}.{self.table} 
            where job_company_linkedin_id in ({bfhs})
        '''
        value = job_company_ids
        res = self.db_mysql.execute(sql, value=value)
        return res
