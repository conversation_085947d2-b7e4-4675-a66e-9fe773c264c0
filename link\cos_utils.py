from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from qcloud_cos.cos_exception import CosClientError, CosServiceError
import io
from settings import logging as logger



# COS 配置
COS_SECRET_ID = 'AKIDZzCCxUlrjPkutd8PTKxLWTBuPisnUyDH'
COS_SECRET_KEY = 'sdBVcXEhvviIgqnW0HLAoSt77tRmvrZ4'
COS_REGION = 'ap-hongkong'
COS_BUCKET = 'data-static-files-1313580114'

class CosUtil:
    def __init__(self):
        config = CosConfig(Region=COS_REGION, SecretId=COS_SECRET_ID, SecretKey=COS_SECRET_KEY)
        self.client = CosS3Client(config)

    def upload_file_stream(self, bucket, key, file_bytes):
        """流式上传文件到 COS"""

        response = self.client.put_object(
            Bucket=bucket,
            Key=key,
            Body=file_bytes,
            StorageClass='STANDARD',
        )
        return response
    
    def download_file_as_str(self, bucket, key):
        response = self.client.get_object(
            Bucket=bucket,
            Key=key,
            StorageClass='STANDARD',
        )
        body = response['Body']
        chunks = []
        while True:
            chunk = body.read(4096)
            if not chunk:
                break
            chunks.append(chunk)
        return b''.join(chunks).decode('utf-8')

def main(file_md5, file_str, is_zip):
    cos = CosUtil()
    try:
        if is_zip:
            cos_file_key = f'/linkedin/html/{file_md5}.lz'  # 使用 MD5 作为文件名
            logger.info(f"文件将上传到: {cos_file_key}")
        else:
            cos_file_key = f'/linkedin/html/{file_md5}.html'  # 使用 MD5 作为文件名
            logger.info(f"文件将上传到: {cos_file_key}")

        file_bytes = file_str.encode('utf-8')
        response = cos.upload_file_stream(bucket=COS_BUCKET, key=cos_file_key, file_bytes=file_bytes)
        logger.info(f"上传成功: {response['ETag']}")
        return cos_file_key
    except Exception as e:
        logger.info(f"上传失败:{e}")

def get_html_from_cos(file_md5):
        cos = CosUtil()
        try:
            content = cos.download_file_as_str(bucket=COS_BUCKET, key=file_md5)
            return content
        except Exception as e:
            logger.info(f"下载失败:{e}")


if __name__ == '__main__':
    main()