import logging
from lzstring import LZ<PERSON>tring

from link.preprocess import Preprocessor
from link.route_models import *
import ast
import hashlib
from .cos_utils import main
from public_utils_configs.util.mysql_pool_v3 import MysqlResult
from .models import LinkTask, LinkResultMap
from django.db import transaction
import traceback
from enum import Enum



class TaskStatus(Enum):
    NOT_STARTED = 0  # 未完成
    RUNNING = 1  # 进行中
    SUCCESS = 2    # 已完成
    FAILED = 3       # 失败


class Funcs:
    def __init__(self):
        self.preprocessor_obj = Preprocessor()
        self.lz = LZString()

    def deal_req(self, link_url, html_str, is_zip) -> LinkResultResp:
        """
        解析 领英 页面
        """
        # 临时测试上传cos服务器
        task_md5 = hashlib.md5(html_str.encode('utf-8')).hexdigest()
        cos_path = main(file_md5=task_md5, file_str=html_str, is_zip=is_zip)
        logging.info(f"{cos_path=}")

        if not link_url or not html_str:
            return LinkResultResp()
        if is_zip:
            html_str = self.lz.decompressFromEncodedURIComponent(html_str)
        # with open('./test/test2.html', 'w', encoding='utf-8') as f:
        #     f.write(html_str)
        # 判断该页面链接 需要调用的解析处理方法
        if 'search/results/all' in link_url:
            # 综合搜索页
            logging.debug(f'start 综合搜索页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_search_page(html_str, from_type='html_all')
        elif 'search/results/people' in link_url:
            # 人物搜索页
            logging.debug(f'start 人物搜索页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_search_page(html_str,
                                                                                          from_type='html_people')
        elif 'search/results/companies' in link_url:
            # 公司搜索页
            logging.debug(f'start 公司搜索页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_search_page(html_str,
                                                                                          from_type='html_company')
        elif 'search/results/all' in link_url or 'search/results/people' in link_url or 'search/results/companies' in link_url:
            # 综合搜索页、人物搜索页、公司搜索页
            logging.debug(f'start 综合搜索页、人物搜索页、公司搜索页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_search_page(html_str, from_type='html')
        elif 'linkedin.com/company' in link_url:
            # 公司详情页
            logging.debug(f'start 公司详情页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_company_info_page(html_str)
        elif '/overlay/contact-info/' in link_url:
            logging.debug(f'start 人物联系方式页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_person_contact_page(html_str)
        elif 'linkedin.com/in' in link_url:
            # 人物详情页
            logging.debug(f'start 人物详情页 === {link_url[:100]}')
            person_datalist, company_datalist = self.preprocessor_obj.process_person_info_page(html_str)
        elif 'linkedin.com/voyager/api/graphql' in link_url:
            # 综合搜索页、人物搜索页、公司搜索页
            logging.debug(f'start 综合搜索页、人物搜索页、公司搜索页(json) === {link_url[:100]}')
            search_person_datalist, search_company_datalist = self.preprocessor_obj.process_search_page(
                html_str, from_type='json'
            )
            # # 人物语言
            # languages_person_datalist, languages_company_datalist = self.preprocessor_obj.deal_graphql_languages(
            #     html_str
            # )
            # # 人物工作经历
            # experience_person_datalist, experience_company_datalist = self.preprocessor_obj.deal_graphql_experience(html_str)
            #
            # # 教育经历
            # education_person_datalist, education_company_datalist = self.preprocessor_obj.deal_graphql_education(
            #     html_str
            # )

            person_datalist = search_person_datalist
            company_datalist = search_company_datalist

        else:
            return LinkResultResp()
        human_ids = []
        company_ids = []
        for person_data in person_datalist:
            lid = person_data.get('lid', '')
            if lid:
                human_ids.append(lid)
        for company_data in company_datalist:
            job_company_id = company_data.get('job_company_id', '')
            if job_company_id:
                company_ids.append(job_company_id)
        return LinkResultResp(human_ids=human_ids, company_ids=company_ids)

    def process_task(self, req:LinkAddReq) -> LinkAddResp:
        """
        处理任务
        """
        link_url = req.link_url
        html_str = req.html_str
        is_zip = req.is_zip
        try:
            task_md5 = hashlib.md5(html_str.encode('utf-8')).hexdigest()
            with transaction.atomic(using='db_link'):
                task = LinkTask.objects.using('db_link').select_for_update().filter(task_md5=task_md5).first()
                if task:
                    return LinkAddResp(task_id=task.task_md5)

                cos_path = main(file_md5=task_md5, file_str=html_str, is_zip=is_zip)
                task = LinkTask.objects.using('db_link').create(
                    task_md5=task_md5, cos_path=cos_path, is_zip=is_zip, status=1, link_url=link_url
                )
                from .tasks import process_linkedin_data
                process_linkedin_data.delay(link_url, cos_path, is_zip, task_md5)

                return LinkAddResp(task_id=task.task_md5)
            
        except Exception as e:
            logging.error(f'{traceback.format_exc()}')

    def check_task(self, req:LinkBatchReq) -> Dict:
        try:
            tasks = req.task_id
            with transaction.atomic(using='db_link'):
                res = LinkTask.objects.using('db_link').filter(task_md5=tasks).values_list('task_md5', 'status')

            status_dict = {}

            for task_md5, status in res:
                if status == TaskStatus.NOT_STARTED.value:
                    status_dict[task_md5] = TaskStatus.NOT_STARTED.name
                elif status == TaskStatus.RUNNING.value:
                    status_dict[task_md5] = TaskStatus.RUNNING.name
                elif status == TaskStatus.SUCCESS.value:
                    status_dict[task_md5] = TaskStatus.SUCCESS.name
                elif status == TaskStatus.FAILED.value:
                    status_dict[task_md5] = TaskStatus.FAILED.name

            return status_dict
        except Exception as e:
            logging.error(f'taks_status, error:{e}')

    def get_task_result(self, req:LinkBatchReq) -> Dict:
        try:
            tasks = req.task_id
            with transaction.atomic(using='db_link'):
                res = LinkResultMap.objects.using('db_link').filter(task_md5=tasks).values_list('human_ids', 'company_ids')

            for human_ids, company_ids in res:
                status_dict = {
                    "human_ids": ast.literal_eval(human_ids),
                    "company_ids": ast.literal_eval(company_ids)
                }

            return status_dict
        except Exception as e:
            logging.error(f'result error: {e}')

    def clean_data(self):
        pass


funcs_obj = Funcs()


if __name__ == '__main__':
    import json
    with open('../test/req.json', 'r', encoding='utf-8') as f:
        req = json.load(f)
    req = LinkResultResp(**req)
    data = funcs_obj.deal_req(req)
    print(data)
