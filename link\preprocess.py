# 预处理方法
import datetime
import logging
import re
import json
from typing import Tuple, Union, Dict
from lxml import etree
import public.public_funcs
from link.parse import LinkParser
from link.link_funcs import LinkFuncs
from link.parse_models import *
from _models.company_gz.db_link import *
from curd.company_gz.db_link.tb_linekdin_person_list import TBLinekdinPersonList
from curd.company_gz.db_link.tb_linekdin_person_logo import TBLinekdinPersonLogo
from curd.company_gz.db_link.tb_linekdin_company_logo import TBLinekdinCompanyLogo
from curd.company_gz.db_link.tb_linekdin_person_education import TBLinekdinPersonEducation
from curd.company_gz.db_link.tb_linekdin_person_experience import TBLinekdinPersonExperience
from curd.company_gz.db_link.tb_linekdin_company_base_info_id import TBLinekdinCompanyBaseInfoId
from curd.company_gz.db_std_map.tb_area import TableArea
from curd.es.idx_linkedin_person import IdxLinkedinPerson
from curd.es.idx_linkedin_company import IdxLinkedinCompany
from connection_instantiation import *
from settings import *


class NoLoginCleanHumanData(LinkFuncs):
    def __init__(self, area_map: dict):
        super().__init__()
        self.area_map = area_map
        self.date_pat = re.compile(r'(\d+)')

    def clean_human_data(self, human_data):
        """
        清洗人物数据
        """
        # 全名
        try:
            full_name = human_data.get('name', '') or human_data.get('reviewedBy', dict()).get('name', '')
        except:
            full_name = ''
        # 人物链接
        try:
            human_url = human_data.get('url', '') or human_data.get('author', dict()).get('url', '')
            linkedin_username = re.findall(r'.*/in/(.*)', human_url)
            linkedin_username = linkedin_username[0] if linkedin_username else ''
        except:
            linkedin_username = ''
        # 职称
        job_title = human_data.get('jobTitle', '')
        job_title = job_title[0] if job_title else ''
        # 当前工作经历
        works_for = human_data.get('worksFor', [])
        job_company_id = ''
        job_company_name = ''
        job_start_date = ''
        if works_for:
            # 公司id,公司名,任职开始时间
            works_for_item = works_for[0]
            company_url = works_for_item.get('url', '')
            job_company_id = re.findall(r'.*/company/(.*)', company_url)
            job_company_id = job_company_id[0] if job_company_id else ''
            job_company_name = works_for_item.get('name', '')
            job_start_date = works_for_item.get('member', {}).get('startDate', '')
        # 职位最后更新时间
        last_updated_date = datetime.datetime.now()
        job_last_updated = f'{last_updated_date.year}' + '-' + "{:0>2d}".format(
            last_updated_date.month) + '-' + "{:0>2d}".format(last_updated_date.day)
        # 人物地址信息
        location_name = human_data.get('address', {}).get('addressLocality', '')
        # 人物所在市区,人物所在州省,人物所在国家,人物所在国家二字码
        area_data_info = self.mate_area_data(location_name)
        location_locality = area_data_info.get('city_en', '')
        location_region = area_data_info.get('province_en', '')
        location_country = area_data_info.get('country_en', '')
        country_ios_code = area_data_info.get('country_iso_code', '')
        # 语言
        languages = []
        knows_language = human_data.get('language_ability', [])
        for item in knows_language:
            languages.append({'name': item.get('language_name', ''), 'proficiency': item.get('language_leval', '')})
        languages = json.dumps(languages)
        return LinkPersonList(full_name=full_name,
                              linkedin_username=linkedin_username,
                              job_title=job_title,
                              job_company_id=job_company_id,
                              job_company_name=job_company_name,
                              job_start_date=job_start_date,
                              job_last_updated=job_last_updated,
                              location_name=location_name,
                              location_locality=location_locality,
                              location_region=location_region,
                              location_country=location_country,
                              country_ios_code=country_ios_code,
                              languages=languages
                              )

    def clean_link_person_experience(self, work_experience_list: list, human_name: str, lid: str):
        """
        清洗工作经历
        """
        experience_item_list = []
        job_company_id_list = []
        company_logo_list = []
        for experience_data in work_experience_list:
            if experience_data.get('company_id', '') in job_company_id_list or not experience_data.get('company_id', ''):
                continue
            job_company_id_list.append(experience_data.get('company_id', ''))
            experience_obj = dict()
            company_logo_obj = dict()
            experience_obj['full_name'] = human_name  # 人名
            experience_obj['lid'] = lid  # lid
            experience_obj['job_company_name'] = experience_data.get('company_name', '')  # 公司名
            experience_obj['job_company_id'] = experience_data.get('company_id', '')  # 领英id
            work_experience = experience_data.get('work_experience', [])  # 当前一家公司下所有经经历过往
            if work_experience:
                local_str = work_experience[0].get('work_location', '')
                experience_obj['job_company_location_name'] = local_str  # 公司所在位置
                area_data_info = self.mate_area_data(local_str)
                experience_obj['job_company_location_country'] = area_data_info.get('country_en', '')  # 国家
                experience_obj['job_company_location_region'] = area_data_info.get('province_en', '')  # 省份
                experience_obj['job_company_location_locality'] = area_data_info.get('city_en', '')  # 城市
                experience_obj['country_ios_code'] = area_data_info.get('country_iso_code', '')  # 国家代码
                # 最开始的任职时间
                start_date_data_en = self.get_date_time(work_experience[-1].get('duration_of_employment', ''),language='en')
                start_date_data_cn = self.get_date_time(work_experience[-1].get('duration_of_employment', ''),language='cn')
                experience_obj['start_date'] = start_date_data_en.get('start_date', '') or start_date_data_cn.get('start_date', '') or ''
                # 最后的任职时间
                end_date_data_en = self.get_date_time(work_experience[0].get('duration_of_employment', ''),language='en')
                end_date_data_cn = self.get_date_time(work_experience[-1].get('duration_of_employment', ''),language='cn')
                experience_obj['end_date'] = end_date_data_en.get('end_date', '') or end_date_data_cn.get('end_date_data_cn', '') or ''
                # 担任职位
                experience_obj['title_name'] = work_experience[0].get('job_title', '')
            # 公司logo
            logo_url = experience_data.get('company_logo_url', '') or ''
            if logo_url and experience_data.get('company_id', ''):
                company_logo_obj['job_company_id'] = experience_data.get('company_id', '')
                company_logo_obj['logo_url'] = logo_url
                company_logo_list.append(LinkCompanyLogo(**company_logo_obj))
            experience_item_list.append(LinkPersonExperience(**experience_obj))

        return experience_item_list, company_logo_list

    def clean_link_person_education(self, education_list, human_name, lid, sid=''):
        clean_education_list = []
        for item in education_list:

            education_obj = dict()
            education_obj['full_name'] = human_name
            education_obj['school_name'] = item.get('school_name', '')
            education_obj['lid'] = lid
            education_obj['sid'] = sid
            education_obj['linkedin_url'] = item.get('study_major', '')
            education_obj['majors'] = item.get('study_major', '')
            learning_duration = item.get('learning_duration', '') or ''
            date_obj_en = self.get_date_time(learning_duration,language='en')
            date_obj_cn = self.get_date_time(learning_duration,language='cn')
            education_obj['start_date'] = date_obj_en.get('start_date') or date_obj_cn.get('start_date')  or ''
            education_obj['end_date'] = date_obj_en.get('end_date') or date_obj_cn.get('end_date')  or ''
            education_obj['summary'] = item.get('details', '') or ''
            clean_education_list.append(LinkPersonEducation(**education_obj))
        return clean_education_list

    def clean_link_person_logo(self, human_data, lid):
        try:
            logo_url = human_data.get('image', dict()).get('contentUrl', '')
        except:
            logo_url = ''
        return LinkPersonLogo(lid=lid, logo_url=logo_url)


class NotLoginCleanCompanyData(LinkFuncs):

    def __init__(self, area_map: dict):
        super().__init__()
        self.area_map = area_map
        self.date_pat = re.compile(r'(\d+)')

    def clean_company_data(self, json_data):
        # 公司大致信息
        company_overview = json_data.get("overview", dict())
        # 地址信息
        address_data = json_data.get('address', dict())
        # logo信息
        logo_data = json_data.get('logo', dict())
        # 数量信息
        human_number_data = json_data.get('numberOfEmployees', dict())
        # 领英公司链接ID
        job_company_url = json_data.get("url", '')
        job_company_id = re.findall('company/(.*)', job_company_url)
        job_company_id = job_company_id[0] if job_company_id else ''
        # 公司名
        job_company_name = json_data.get("name", '') or ''
        # 公司官网
        job_company_website = json_data.get("sameAs", '')
        # 公司规模
        human_number = int(human_number_data.get("value", 0))
        job_company_size = self.public_funcs_obj.clean_company_size(human_number)
        # 公司成立年份
        job_company_founded = company_overview.get('Founded', '')
        # 行业
        job_company_industry = company_overview.get('Industry', '')
        # 公司领英链接
        job_company_linkedin_url = json_data.get('url', '')
        job_company_linkedin_url_index = job_company_linkedin_url.find('linkedin')
        job_company_linkedin_url = job_company_linkedin_url[job_company_linkedin_url_index:]
        # 邮编
        address_data.get("postalCode", "")
        job_company_location_postal_code = str(address_data.get("postalCode", ""))
        # 公司地址信息
        addressRegion = str(address_data.get("addressRegion", ""))
        addressLocality = str(address_data.get("addressLocality", ""))
        company_country_data = str(address_data.get("addressCountry", ""))
        job_company_location_name_list = [
            addressLocality,
            addressRegion,
            company_country_data
        ]
        job_company_location_name = ','.join(job_company_location_name_list)
        # 公司所属国家,省份,城市
        job_company_location_data = self.mate_area_data(job_company_location_name)
        job_company_location_country = job_company_location_data.get('country_en', '')
        job_company_location_locality = job_company_location_data.get('province_en', '')
        job_company_location_region = job_company_location_data.get('city_en', '')
        # 街道
        job_company_location_street_address = address_data.get('streetAddress', '')
        # 关于-领域
        specialities = company_overview.get('Industry', '')
        # 关于-概览
        description = json_data.get('description', '')
        # 公司logo
        logo_url = logo_data.get('contentUrl', '')
        # 国家代码
        country_ios_code = job_company_location_data.get('country_iso_code', '')
        # 职务发布链接
        job_search_url = json_data.get('jod_url', '')
        # 公司数据
        company_data = {
            'job_company_id': job_company_id,
            'job_company_name': job_company_name,
            'job_company_website': job_company_website,
            'job_company_size': job_company_size,
            'job_company_founded': job_company_founded,
            'job_company_industry': job_company_industry,
            'job_company_linkedin_url': job_company_linkedin_url,
            'job_company_location_locality': job_company_location_locality,
            'job_company_location_region': job_company_location_region,
            'job_company_location_name': job_company_location_name,
            'job_company_location_country': job_company_location_country,
            'job_company_location_postal_code': job_company_location_postal_code,
            'job_company_location_street_address': job_company_location_street_address,
            'specialities': specialities,
            'description': description,
            'country_ios_code': country_ios_code,
            'job_search_url': job_search_url
        }
        # 高管数据
        human_list = [LinkPersonList(**human_data) for human_data in json_data['human_list']]

        return (LinkCompanyBaseInfoId(**company_data), self.clean_link_company_logo(job_company_id=job_company_id, logo_url=logo_url), human_list)

    def clean_link_company_logo(self, job_company_id, logo_url):

        return LinkCompanyLogo(job_company_id=job_company_id, logo_url=logo_url,)


class LoginCompanyCleanData(LinkFuncs):

    def __init__(self, area_map: dict):
        super().__init__()
        self.area_map = area_map
        self.date_pat = re.compile(r'(\d+)')
        self.company_name_pat = re.compile(r'"name":"(.*?)","tagline"')

    def clean_company_data(self, data_obj):
        html_str = data_obj.get('html_str', [])
        company_id = data_obj.get('company_name', '')
        if not company_id or not html_str:
            return dict(), list()
        # 领英公司ID(领英公司链接ID)
        # 公司名称
        company_data, company_logo_list = self.get_company_json_data(company_id, html_str)
        if company_data:
            return company_data, company_logo_list
        return dict(), list()

    def get_job_company_industry(self, included_list, code_list):
        company_industry_data_str = []
        for code in code_list:
            for data in included_list:
                if data.get("entityUrn") == code and data.get("name"):
                    company_industry_data_str.append(data.get("name"))
                    break
        return ','.join(company_industry_data_str)

    def get_company_json_data(self, company_id, html_str):
        parse_company_data = dict()
        company_logo_list = []
        html_obj = etree.HTML(html_str)
        job_company_name_xpath = './/h1[contains(@class,"ember-view org-top-card-summary__title")]//text()'
        job_company_website_xpath = './/a[@rel="noopener noreferrer"]/@href'
        job_company_size_xpath = './/span[@class="t-normal t-black--light link-without-visited-state link-without-hover-state"]//text()'
        job_company_industry_xpath = './/div[@class="org-top-card-summary-info-list"]/div[@class="org-top-card-summary-info-list__info-item"]//text()'
        job_company_location_name_xpath = '(.//div[@class="org-top-card-summary-info-list"]/div[@class="inline-block"]/div[@class="org-top-card-summary-info-list__info-item"])[1]//text()'
        description_xpath = './/div[@class="ember-view organization-about-module__content-consistant-cards-description"]/span//text()'
        logo_xpath = './/div[contains(@class,"org-top-card-primary-content__logo-container")]//@src'
        job_search_url_xpath = './/a[contains(@class, "ember-view pv3 ph4 t-16 t-bold t-black--light org-page-navigation__item-anchor")]/@href'
        # 领英公司ID(领英公司链接ID)    &-son
        parse_company_data['job_company_id'] = company_id
        # 公司名
        job_company_name = html_obj.xpath(job_company_name_xpath)
        parse_company_data['job_company_name'] = job_company_name[0].strip().replace('\n','') if job_company_name else ''
        # 公司官网
        job_company_website = html_obj.xpath(job_company_website_xpath)
        job_company_website = job_company_website[0].strip().replace('\n','').strip('#')
        parse_company_data['job_company_website'] = job_company_website if not 'linkedin' in job_company_website else ''
        # 公司规模
        job_company_size = html_obj.xpath(job_company_size_xpath)
        job_company_size = job_company_size[0].strip().replace('\n','').replace(',','') if job_company_size else ''
        if job_company_size.find(' ') >= 0 and job_company_size.find('-') >= 0:
            split_start_index = job_company_size.find('-') + 1
            split_end_index = job_company_size.find(' ')
            job_company_size = job_company_size[split_start_index:split_end_index]
            try:
                job_company_size = self.public_funcs_obj.clean_company_size(int(job_company_size))
            except:
                job_company_size = ''
        else:
            job_company_size = ''
        parse_company_data['job_company_size'] = job_company_size
        # 公司行业
        job_company_industry = html_obj.xpath(job_company_industry_xpath)
        parse_company_data['job_company_industry'] = job_company_industry[0].strip().replace('\n','') if job_company_industry and job_company_industry[0] else ''
        # 公司领英链接
        parse_company_data['job_company_linkedin_url'] = f'linkedin.com/company/{company_id}' if company_id and company_id[0] != 'all' else ''
        # 地址
        job_company_location_name = html_obj.xpath(job_company_location_name_xpath)
        job_company_location_name = job_company_location_name[0].strip().replace('\n','') if job_company_location_name else ''
        job_company_location_data = self.mate_area_data(job_company_location_name)
        parse_company_data['job_company_location_name'] = job_company_location_name
        parse_company_data['job_company_location_locality'] = job_company_location_data.get('city_en','') if job_company_location_data.get('city_en', '') else ''
        parse_company_data['job_company_location_region'] = job_company_location_data.get('province_en','') if job_company_location_data.get('province_en', '') else ''
        parse_company_data['job_company_location_country'] = job_company_location_data.get('country_en','') if job_company_location_data.get('country_en', '') else ''
        # 公司概览
        description = html_obj.xpath(description_xpath)
        description = ' '.join([data.replace('\n', '').strip() for data in description])
        parse_company_data['description'] = description
        # 公司logo
        logo_url = html_obj.xpath(logo_xpath)
        logo_url = logo_url[0] if logo_url else ''
        if logo_url:
            company_logo_obj = {
                'job_company_id': parse_company_data['job_company_id'],
                'logo_url': logo_url
            }
            company_logo_list.append(company_logo_obj)
        # 职位
        job_search_url = html_obj.xpath(job_search_url_xpath)
        job_search_url = job_search_url[0] if job_search_url else ''
        parse_company_data['job_search_url'] = 'https://www.linkedin.com' + job_search_url if job_search_url.find('https://www.linkedin') < 0 else job_search_url
        return parse_company_data, company_logo_list


class LoginHumanCleanData(LinkFuncs):

    def __init__(self, area_map: dict):
        super().__init__()
        self.area_map = area_map
        self.date_pat = re.compile(r'(\d+)')

    def clean_human_data(self, human_data):
        """
        human_data  主页返回的(先清洗出人物基本信息,名字,用户id,工作名)
        """
        linkedin_username = human_data.get('human_name', '') or ''
        person_data = dict()
        person_logo_url_data = dict()
        education_data_list = list()
        experience_data_list = list()
        company_logo_list = list()
        languages_data_list = list()
        skills_data_list = list()
        html_obj = human_data.get('html_str')
        if not html_obj or not linkedin_username:
            return person_data,person_logo_url_data,education_data_list,experience_data_list,company_logo_list
        html_obj = etree.HTML(html_obj)
        full_name_xpath = './/h1//text()'
        location_name_xpath = './/span[@class="text-body-small inline t-black--light break-words"]//text()'
        logo_url_xpath = './/div[@class="pv-top-card__non-self-photo-wrapper ml0"]//img/@src'
        profiles_xpath = './/div[@class="text-body-medium break-words"]//text()'
        languages_list_xpath = '(.//div[@id="languages"]/following-sibling::div[2]/ul/li)'
        experience_list_xpath = '(.//div[@id="experience"]/following-sibling::div[2]/ul/li)'
        education_list_xpath = '(.//div[@id="education"]/following-sibling::div[2]/ul/li)'
        skills_xpath =  '(.//div[@id="skills"]/following-sibling::div[2]/ul/li)'
        # 全名
        full_name = html_obj.xpath(full_name_xpath)
        full_name = full_name[0].strip().replace('\n','') if full_name else ''
        # 地点名称
        location_name = html_obj.xpath(location_name_xpath)
        location_name = location_name[0].strip().replace('\n','') if location_name else ''
        # 解析出的地点
        location_data = self.mate_area_data(location_name)
        location_locality = (location_data.get('city_en', '') or '').strip()
        location_region = (location_data.get('province_en', '') or '').strip()
        location_country= (location_data.get('country_en', '') or '').strip()
        country_ios_code = (location_data.get('country_iso_code', '') or '').strip()
        # 简介
        profiles = html_obj.xpath(profiles_xpath)
        profiles = ','.join([d.replace('\n', '').strip() for d in profiles if d.replace('\n', '').strip()])
        # 头像
        logo_url = html_obj.xpath(logo_url_xpath)
        logo_url = logo_url[0] if logo_url else ''
        # 语言
        languages_items_list = html_obj.xpath(languages_list_xpath)
        for languages_index in range(len(languages_items_list)):
            language_xpath = f'{languages_list_xpath}[{languages_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[@class="display-flex flex-row justify-space-between"]/div/div[@class="display-flex flex-wrap align-items-center full-height"]/div/div/div/span//text()'
            proficiency_xpath = f'{languages_list_xpath}[{languages_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[@class="display-flex flex-row justify-space-between"]/div/span/span[@class="pvs-entity__caption-wrapper"]//text()'
            language = html_obj.xpath(language_xpath)
            proficiency = html_obj.xpath(proficiency_xpath)
            if not language or not language[0]:
                continue
            languages_obj = {
                'name':language[0].replace('\n','').strip(),
                'proficiency':proficiency[0] if proficiency else '',
            }
            languages_data_list.append(languages_obj)
        # 教育经历
        education_items_list = html_obj.xpath(education_list_xpath)
        for education_index in range(len(education_items_list)):
            school_name_xpath = f'{education_list_xpath}[{education_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[@class="display-flex flex-row justify-space-between"]/a[contains(@class,"optional-action-target-wrapper")]/div[@class="display-flex flex-wrap align-items-center full-height"]/div/div/div/span//text()'
            majors_xpath = f'{education_list_xpath}[{education_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[@class="display-flex flex-row justify-space-between"]/a[contains(@class,"optional-action-target-wrapper")]/span[@class="t-14 t-normal"]/span[@aria-hidden="true"]//text()'
            date_xpath = f'{education_list_xpath}[{education_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[@class="display-flex flex-row justify-space-between"]/a[contains(@class,"optional-action-target-wrapper")]/span[contains(@class,"t-black--light")]/span[@class="pvs-entity__caption-wrapper"]//text()'
            summary_xpath = f'{education_list_xpath}[{education_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[contains(@class,"pvs-entity__sub-components")]/ul/li/div/ul/li/div/div/div/div/span[@aria-hidden="true"]//text()'
            school_linkedin_url_xpath = f'{education_list_xpath}[{education_index+1}]/div/div[contains(@class,"display-flex flex-column align-self-center")]/div[@class="display-flex flex-row justify-space-between"]/a/@href'
            school_name = html_obj.xpath(school_name_xpath)
            school_name = school_name[0].replace('\n', '') if school_name else ''
            majors_data = html_obj.xpath(majors_xpath) if school_name else ''
            majors_data = majors_data[0].replace('\n', '') if majors_data else ''
            majors_data = [d for d in majors_data.split(',') if d]
            if len(majors_data) == 2:
                degrees = json.dumps([majors_data[0].strip().replace('\n','')]) if majors_data[0].strip().replace('\n','') else ''
                majors = json.dumps([majors_data[1].strip().replace('\n','')]) if majors_data[1].strip().replace('\n','') else ''
            else:
                majors = ''
                degrees = ''
            date = html_obj.xpath(date_xpath)
            date = date[0].replace('\n', '') if date else ''
            summary = html_obj.xpath(summary_xpath)
            summary = ' '.join([summary_str.replace('\n', '').strip() for summary_str in summary if summary_str.replace('\n', '').strip()])
            school_linkedin_url = html_obj.xpath(school_linkedin_url_xpath)
            school_linkedin_url = school_linkedin_url[0].replace('\n', '').strip() if school_linkedin_url else ''
            date_obj_en = self.get_date_time(date,language='en')
            date_obj_cn = self.get_date_time(date,language='cn')
            start_date = date_obj_en.get('start_date') or date_obj_cn.get('start_date') or ''
            end_date = date_obj_en.get('end_date') or date_obj_cn.get('end_date') or ''
            if not school_name:
                continue
            linkedin_id = re.findall(r'company/(.*?)/',school_linkedin_url) or re.findall(r'school/(.*?)/',school_linkedin_url)
            linkedin_id = linkedin_id[0] if linkedin_id else ''
            linkedin_url = ''
            if linkedin_id:
                linkedin_url_index = school_linkedin_url.find('linkedin')
                linkedin_url = school_linkedin_url[linkedin_url_index:] if linkedin_url_index else ''
            education_obj = {
                'school_name': school_name or '',
                'linkedin_id': linkedin_id or '',
                'linkedin_url': linkedin_url or '',
                'majors': majors or '',
                'degrees':degrees or '',
                'summary': summary or '',
                'end_date': end_date or '',
                'start_date': start_date or '',
            }
            if education_obj.get('school_name'):
                education_data_list.append(education_obj)
        # 工作经历
        experience_items_list = html_obj.xpath(experience_list_xpath)
        for experience_index in range(len(experience_items_list)):
            # 先判断一个工作经历下是否含有多个经历
            multiple_experience_xath = f'({experience_list_xpath}[{experience_index+1}]/div/div[2]/div[contains(@class,"pvs-entity__sub-components")]/ul/li)'
            multiple_experience_items = html_obj.xpath(multiple_experience_xath)
            # 如果没有这个情况,就走第一套解析,反之,则走第二套
            if len(multiple_experience_items) == 0:
                job_company_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//div/span[@aria-hidden="true"]//text()'
                title_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/span/span[@aria-hidden="true"]//text()'
                date_str_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/span[2]/span[@aria-hidden="true"]//text()'
                job_company_location_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/span[3]/span[@aria-hidden="true"]//text()'
                job_company_linkedin_url_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/@href'
                logo_url_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[1]//img/@src'

                job_company_name = html_obj.xpath(job_company_name_xpath)
                job_company_name = job_company_name[0].replace('\n', '').strip() if job_company_name else ''

                title_name = html_obj.xpath(title_name_xpath)
                title_name = title_name[0].replace('\n', '').strip() if title_name else ''

                date_str = html_obj.xpath(date_str_xpath)
                date_str = date_str[0].replace('\n', '').strip() if date_str else ''
                date_obj_en = self.get_date_time(date_str, language='en')
                date_obj_cn = self.get_date_time(date_str, language='cn')
                start_date = date_obj_en.get('start_date') or date_obj_cn.get('start_date') or ''
                end_date = date_obj_en.get('end_date') or date_obj_cn.get('end_date') or ''

                job_company_location_name = html_obj.xpath(job_company_location_name_xpath)
                job_company_location_name = job_company_location_name[0].replace('\n', '').strip() if job_company_location_name else ''
                job_company_location = self.mate_area_data(job_company_location_name)
                job_company_location_locality = job_company_location.get('city_en', '') or ''
                job_company_location_region = job_company_location.get('province_en', '') or ''
                job_company_location_country = job_company_location.get('country_en', '') or ''
                country_ios_code = job_company_location.get('country_ios_code', '') or ''

                job_company_linkedin_url = html_obj.xpath(job_company_linkedin_url_xpath)
                job_company_linkedin_url = job_company_linkedin_url[0].replace('\n', '').strip() if job_company_linkedin_url and '/company/' in job_company_linkedin_url[0] else ''
                job_company_linkedin_url = job_company_linkedin_url[job_company_linkedin_url.find('linkedin'):] if job_company_linkedin_url.find('linkedin')>=0 else ''

                company_logo_url = html_obj.xpath(logo_url_xpath)
                company_logo_url = company_logo_url[0].replace('\n', '').strip() if company_logo_url else ''
                job_company_id  = re.findall(r'company/(.*?)/',job_company_linkedin_url)
                job_company_id = job_company_id[0].replace('\n', '').strip() if job_company_id and job_company_id[0] != 'all' else ''
                if not job_company_name:
                    continue
                experience_obj = {
                    'job_company_name': (job_company_name or '').lower().replace('· Full-time','').replace('· 全职', '').replace('· 兼职','').replace('· 自由职业','').replace('· Part-time', '').strip(),
                    'job_company_id': (job_company_id or '').lower(),
                    'job_company_linkedin_url': (job_company_linkedin_url or '').lower(),
                    'job_company_linkedin_id':(job_company_id or '').lower(),
                    'job_company_location_name': (job_company_location_name or '').lower(),
                    'job_company_location_locality': (job_company_location_locality or '').lower(),
                    'job_company_location_region': (job_company_location_region or '').lower(),
                    'job_company_location_country': (job_company_location_country or '').lower(),
                    'country_ios_code':country_ios_code or '',
                    'start_date': start_date or '',
                    'end_date': end_date or '',
                    'title_name': title_name or '',
                }
                if company_logo_url:
                    company_logo_list.append({'job_company_id':job_company_id,'logo_url':company_logo_url})
                experience_data_list.append(experience_obj)

            elif len(multiple_experience_items) > 0:
                job_company_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//div/span[@aria-hidden="true"]//text()'
                title_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/span/span[@aria-hidden="true"]//text()'
                date_str_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/span[2]/span[@aria-hidden="true"]//text()'
                job_company_location_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/span[3]/span[@aria-hidden="true"]//text()'
                job_company_linkedin_url_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/@href'
                job_company_logo_url_xpath = f'{experience_list_xpath}[{experience_index+1}]//div/img/@src'
                exit_job_company_location_name_xpath =f'{experience_list_xpath}[{experience_index + 1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/div/span'  # 判断是否存在地点的节点
                # 判断一家公司下的多个经历到底是工作动态还是工作经历,可以判断下面有没有span标签
                if html_obj.xpath(f'{multiple_experience_xath}[1]/span'):
                    job_company_name = html_obj.xpath(job_company_name_xpath)
                    job_company_name = job_company_name[0].replace('\n', '').strip() if job_company_name else ''
                else:
                    job_company_name_xpath = f'{experience_list_xpath}[{experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//div/span[@aria-hidden="true"]//text()'
                    job_company_name = html_obj.xpath(job_company_name_xpath)
                    job_company_name = job_company_name[0].replace('\n', '').strip() if job_company_name else ''

                title_name = html_obj.xpath(title_name_xpath)
                title_name = title_name[0].replace('\n', '').strip() if title_name else ''
                if not title_name:
                    title_name_xpath = f'{multiple_experience_xath}[1]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//div[@class="display-flex flex-wrap align-items-center full-height"]//span[@aria-hidden="true"]/text()'
                    title_name = html_obj.xpath(title_name_xpath)
                    title_name = title_name[0].replace('\n', '').strip() if title_name else ''

                job_company_linkedin_url = html_obj.xpath(job_company_linkedin_url_xpath)
                job_company_linkedin_url = job_company_linkedin_url[0].replace('\n', '').strip() if job_company_linkedin_url and  '/company/' in job_company_linkedin_url[0]  else ''
                if not job_company_linkedin_url:
                    job_company_linkedin_url_xpath = f'{experience_list_xpath}[{experience_index + 1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a/@href'
                    job_company_linkedin_url = html_obj.xpath(job_company_linkedin_url_xpath)
                    job_company_linkedin_url = job_company_linkedin_url[0].replace('\n','').strip() if job_company_linkedin_url and  '/company/' in job_company_linkedin_url[0]  else ''
                job_company_linkedin_url = job_company_linkedin_url[job_company_linkedin_url.find('linkedin'):] if job_company_linkedin_url.find('linkedin') >= 0 else ''

                if len(html_obj.xpath(exit_job_company_location_name_xpath))>2:
                    job_company_location_name = html_obj.xpath(job_company_location_name_xpath)
                    job_company_location_name = job_company_location_name[0].replace('\n', '').strip() if job_company_location_name else ''
                else:
                    job_company_location_name = ''

                if not job_company_location_name and len(html_obj.xpath(exit_job_company_location_name_xpath))>2:
                    for multiple_experience_index in range(len(multiple_experience_items)):
                        job_company_location_name_xpath = f'{multiple_experience_xath}[{multiple_experience_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//span[2]/span[@aria-hidden="true"]//text()'
                        job_company_location_name = html_obj.xpath(job_company_location_name_xpath)
                        if  job_company_location_name and job_company_location_name[0].replace('\n','').strip():
                            job_company_location_name = job_company_location_name[0].replace('\n','').strip()
                            break

                job_company_location = self.mate_area_data(job_company_location_name)
                job_company_location_locality = job_company_location.get('city_en', '') or ''
                job_company_location_region = job_company_location.get('province_en', '') or ''
                job_company_location_country = job_company_location.get('country_en', '') or ''

                date_str = html_obj.xpath(date_str_xpath)
                date_str = date_str[0].replace('\n', '').strip() if date_str else ''

                if not date_str:
                    start_date_xpath = f'{multiple_experience_xath}[last()]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//span[1]/span[@aria-hidden="true"]//text()'
                    end_date_xpath = f'{multiple_experience_xath}[1]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]//span[1]/span[@aria-hidden="true"]//text()'
                    start_date_obj = html_obj.xpath(start_date_xpath)
                    start_date_obj = start_date_obj[0].replace('\n', '').strip() if start_date_obj else ''
                    end_date_obj = html_obj.xpath(end_date_xpath)
                    end_date_obj = end_date_obj[0].replace('\n', '').strip() if end_date_obj else ''
                    start_date_obj_en = self.get_date_time(start_date_obj,language='en')
                    start_date_obj_cn = self.get_date_time(start_date_obj, language='cn')
                    end_date_obj_en = self.get_date_time(end_date_obj,language='en')
                    end_date_obj_cn = self.get_date_time(end_date_obj, language='cn')
                    start_date = start_date_obj_en.get('start_date') or start_date_obj_cn.get('start_date') or ''
                    end_date = end_date_obj_en.get('end_date') or end_date_obj_cn.get('start_date') or ''
                else:
                    date_obj_en = self.get_date_time(date_str, language='en')
                    date_obj_cn = self.get_date_time(date_str, language='cn')
                    start_date = date_obj_en.get('start_date') or date_obj_cn.get('start_date') or ''
                    end_date = date_obj_en.get('end_date') or date_obj_cn.get('end_date') or ''

                job_company_id = re.findall(r'company/(.*?)/', job_company_linkedin_url)
                job_company_id = job_company_id[0].replace('\n', '').strip() if job_company_id and job_company_id[0] != 'all' else ''
                company_logo_url = html_obj.xpath(job_company_logo_url_xpath)
                company_logo_url = company_logo_url[0].replace('\n', '').strip() if company_logo_url else ''
                if not job_company_name:
                    continue
                experience_obj = {
                    'job_company_name':  (job_company_name or '').lower().replace('· Full-time','').replace('· 全职', '').replace('· 兼职','').replace('· 自由职业','').replace('· Part-time', '').strip(),
                    'job_company_id': (job_company_id or '').lower(),
                    'job_company_linkedin_url': (job_company_linkedin_url or '').lower(),
                    'job_company_linkedin_id': (job_company_id or '').lower(),
                    'job_company_location_name': (job_company_location_name or '').lower(),
                    'job_company_location_locality': (job_company_location_locality or '').lower(),
                    'job_company_location_region': (job_company_location_region or '').lower(),
                    'job_company_location_country': (job_company_location_country or '').lower(),
                    'country_ios_code': country_ios_code or '',
                    'start_date': start_date or '',
                    'end_date': end_date or '',
                    'title_name': title_name or '',
                }
                if company_logo_url:
                    company_logo_list.append({'job_company_id': job_company_id, 'logo_url': company_logo_url})
                experience_data_list.append(experience_obj)
        # 技能
        skills_items_list = html_obj.xpath(skills_xpath)
        for skills_index in range(len(skills_items_list)):
            item_xpath = f'{skills_xpath}[{skills_index+1}]/div/div[2]/div[@class="display-flex flex-row justify-space-between"]/a//span[@aria-hidden="true"]//text()'
            skills = html_obj.xpath(item_xpath)
            skills = skills[0].replace('\n', '').strip() if skills and skills[0].replace('\n', '').strip() else ''
            if not skills:
                continue
            skills_data_list.append(skills)
        person_data['linkedin_username'] = linkedin_username
        person_data['full_name'] = full_name
        person_data['location_name'] = location_name
        person_data['location_locality'] = location_locality
        person_data['location_region'] = location_region
        person_data['location_country'] = location_country
        person_data['country_ios_code'] = country_ios_code
        person_data['profiles'] = profiles
        person_data['languages'] = json.dumps(languages_data_list)
        person_data['skills'] = json.dumps(skills_data_list) if skills_data_list else ''
        person_logo_url_data['logo_url']=logo_url
        return person_data,person_logo_url_data,education_data_list,experience_data_list,company_logo_list

    def clean_human_contact(self, human_data) -> Dict:
        """
        人物详情页联系方式
        """
        linkedin_username = human_data.get('human_name', '') or ''
        html_obj = human_data.get('html_str')
        contact_data = {}
        if not html_obj or not linkedin_username:
            return contact_data
        
        html_obj = etree.HTML(html_obj)

        full_name_xpath = './/h1//text()'
        phone_xpath = '//h3[contains(@class, "pv-contact-info__header t-16 t-black t-bold") and contains(text(), "Phone")]/following-sibling::ul//span/text()'
        email_xpath = '//h3[contains(@class, "pv-contact-info__header t-16 t-black t-bold") and contains(text(), "Email")]/following-sibling::div//a/text()'
        birthday_xpath = '//h3[contains(@class, "pv-contact-info__header t-16 t-black t-bold") and contains(text(), "Birthday")]/following-sibling::div//span/text()'
        connected_xpath = '//h3[contains(@class, "pv-contact-info__header t-16 t-black t-bold") and contains(text(), "Connected")]/following-sibling::div//span/text()'

        full_name = html_obj.xpath(full_name_xpath)
        full_name = full_name[0].strip().replace('\n','') if full_name else ''

        phone = html_obj.xpath(phone_xpath)
        phone = [item.strip().replace('\n','') for item in phone] if phone else ''

        email = html_obj.xpath(email_xpath)
        email = [
            {
                "address": item.strip().replace('\n',''),
                "type": "personal"
            }
            for item in email
        ] if email else ''

        birthday = html_obj.xpath(birthday_xpath)
        birthday = birthday[0].strip().replace('\n','') if birthday else ''

        connected = html_obj.xpath(connected_xpath)
        connected = connected[0] if connected else ''

        contact_data['linkedin_username'] = linkedin_username
        contact_data['full_name'] = full_name
        contact_data['mobile_phone'] = json.dumps(phone)
        contact_data['emails'] = json.dumps(email)
        contact_data['birth_date'] = birthday
        # contact_data['connected'] = connected

        return contact_data



class Preprocessor(LinkFuncs):
    def __init__(self):
        super().__init__()
        self.link_parser_obj = LinkParser()
        # 实例化数据表读写操作
        self.tb_link_person_list_obj = TBLinekdinPersonList(uptook_tdsql_comp_mysql_obj)
        self.tb_link_person_logo_obj = TBLinekdinPersonLogo(uptook_tdsql_comp_mysql_obj)
        self.tb_link_company_logo_obj = TBLinekdinCompanyLogo(uptook_tdsql_comp_mysql_obj)
        self.tb_link_person_education_obj = TBLinekdinPersonEducation(uptook_tdsql_comp_mysql_obj)
        self.tb_link_person_experience_obj = TBLinekdinPersonExperience(uptook_tdsql_comp_mysql_obj)
        self.tb_link_company_base_info_id_obj = TBLinekdinCompanyBaseInfoId(uptook_tdsql_comp_mysql_obj)
        self.table_area_obj = TableArea(uptook_tdsql_comp_mysql_obj)

        self.idx_linkedin_person_obj = IdxLinkedinPerson()
        self.idx_linkedin_company_obj = IdxLinkedinCompany()

        self.area_map = self.table_area_obj.get_area_map()

        # link人物详情未登录页清洗
        self.no_login_clean_human_data_obj = NoLoginCleanHumanData(self.area_map)
        # link人物详情登录页清洗
        self.login_clean_human_data_obj = LoginHumanCleanData(self.area_map)
        # link公司未登录页清洗
        self.no_login_clean_company_obj = NotLoginCleanCompanyData(self.area_map)
        # link公司登录页清洗
        self.login_clean_company_obj = LoginCompanyCleanData(self.area_map)

        # 存储 ES所需字段
        self.es_person_fields = [
            "id", "lid", "full_name", "gender", "linkedin_username",
            "facebook_username", "twitter_username", "github_username", "work_email", "mobile_phone",
            "industry",
            "job_title", "job_title_role", "job_title_sub_role", "job_title_levels", "job_company_id",
            "location_name",
            "phone_numbers", "emails", "confidence", "location_locality", "location_region", "country_ios_code",
            "job_company_name", "rate"
        ]
        self.es_company_fields = [
            "id", "job_company_id", "job_company_name", "job_company_industry", "job_company_website",
            "job_company_linkedin_url", "logo_url", "specialities", "description", "country_ios_code",
            "phone", "email", "job_company_facebook_url", "job_company_twitter_url", "job_company_founded",
            "job_company_location_region", "job_company_size", "num_staff", "num_staff_phone", "num_staff_email"
        ]


    def mapping_search_model_company(self, parse_model: ParseSearchAllCompany) -> Tuple[Union[LinkCompanyBaseInfoId, None], List[LinkCompanyLogo]]:
        """
        映射 搜索模型 为 库表模型 - 公司模型
        """
        # print(parse_model.__dict__)
        region = parse_model.region
        area_data_info = self.mate_area_data(region)
        link_company_base_info_id_obj = LinkCompanyBaseInfoId(
            job_company_id=parse_model.job_company_id,
            job_company_name=parse_model.job_company_name,
            job_company_industry=parse_model.industry,
            job_company_linkedin_url=parse_model.job_company_linkedin_url,
            job_company_linkedin_id=parse_model.job_company_linkedin_id,
            job_company_location_name=parse_model.region,
            description=parse_model.company_profiles,
            job_search_url=parse_model.job_search_url[:1000],
            country_ios_code=area_data_info.get('country_iso_code', ''),
            job_company_location_country=area_data_info.get('country_en', ''),
            job_company_location_region=area_data_info.get('province_en', ''),
            job_company_location_locality=area_data_info.get('city_en', ''),
        )
        job_company_id = link_company_base_info_id_obj.job_company_id
        link_company_logo_models = []
        company_logos = parse_model.company_logos
        if company_logos:
            company_logos = json.loads(company_logos)
            for url in company_logos:
                if not url:
                    continue
                link_company_logo_obj = LinkCompanyLogo(
                    job_company_id=job_company_id,
                    logo_url=url,
                )
                link_company_logo_models.append(link_company_logo_obj)
        return link_company_base_info_id_obj, link_company_logo_models

    def mapping_search_model_people(self, parse_model: ParseSearchAllPeople) -> Tuple[Union[LinkPersonList, None], List[LinkPersonLogo]]:
        """
        映射 搜索模型 为 库表模型 - 人物模型
        """
        region = parse_model.region
        area_data_info = self.mate_area_data(region)
        link_person_list_obj = LinkPersonList(
            lid=parse_model.lid,
            full_name=parse_model.full_name,
            linkedin_username=parse_model.linkedin_username,
            profiles=parse_model.person_profiles,
            location_name=parse_model.region,
            country_ios_code=area_data_info.get('country_iso_code', ''),
            location_country=area_data_info.get('country_en', ''),
            location_region=area_data_info.get('province_en', ''),
            location_locality=area_data_info.get('city_en', ''),
        )
        lid = link_person_list_obj.lid
        link_person_logo_models = []
        person_logos = parse_model.person_logos
        if person_logos:
            person_logos = json.loads(person_logos)
            for url in person_logos:
                if not url:
                    continue
                link_person_logo_obj = LinkPersonLogo(
                    lid=lid,
                    logo_url=url,
                )
                link_person_logo_models.append(link_person_logo_obj)
        return link_person_list_obj, link_person_logo_models

    def process_search_page(self, content: str, from_type: str) -> Tuple[List[dict], List[dict]]:
        """
        处理 搜索页
        """
        # 解析 搜索页结果
        if from_type.startswith('html'):
            # 来自源码文本
            data = self.link_parser_obj.search_all_parse_obj.search_all_page_parser_html(content, from_type)
        else:
            data, status = self.link_parser_obj.search_all_parse_obj.search_all_page_parser_json(content)
        company_models = data['company_models']
        people_models = data['people_models']
        # 匹配 库里的 lid
        linkedin_username_list = []
        for _id, model_obj in people_models.items():
            linkedin_username = model_obj.linkedin_username
            linkedin_username_list.append(linkedin_username)
        res = self.tb_link_person_list_obj.search_data_by_link_username(linkedin_username_list)
        link_username_lid_map = {}
        if res:
            existed_datalist = res.data
            for existed_data in existed_datalist:
                _lid = existed_data['lid']
                _linkedin_username = existed_data['linkedin_username']
                _linkedin_username_clean = existed_data['linkedin_username_clean']
                link_username_lid_map[_linkedin_username] = _lid
                link_username_lid_map[_linkedin_username_clean] = _lid

        # 模型映射
        job_company_id_list = []
        link_person_list_models = []
        link_person_logo_models = []
        link_company_logo_models = []
        link_company_base_info_id_models = []
        # 将匹配到的 lid 添加到 解析模型中
        for _id, model_obj in people_models.items():
            linkedin_username = model_obj.linkedin_username
            lid = link_username_lid_map.get(linkedin_username, '')
            if lid:
                model_obj.lid = lid
            link_person_list_obj, _link_person_logo_models = self.mapping_search_model_people(model_obj)
            if link_person_list_obj:
                link_person_list_models.append(link_person_list_obj)
            link_person_logo_models += _link_person_logo_models
        for _id, model_obj in company_models.items():
            job_company_id = model_obj.job_company_id
            job_company_id_list.append(job_company_id)
            link_company_base_info_id_obj, _link_company_logo_models = self.mapping_search_model_company(model_obj)
            if link_company_base_info_id_obj:
                link_company_base_info_id_models.append(link_company_base_info_id_obj)
            link_company_logo_models += _link_company_logo_models
        # 存储更新数据
        if link_person_list_models:
            res = self.tb_link_person_list_obj.save_data(link_person_list_models)
            if res:
                logging.debug(f'linekdin_person_list 更新影响: {res.rowcount=}')
        if link_person_logo_models:
            res = self.tb_link_person_logo_obj.save_data(link_person_logo_models)
            if res:
                logging.debug(f'linekdin_person_logo 更新影响: {res.rowcount=}')
        if link_company_logo_models:
            res = self.tb_link_company_logo_obj.save_data(link_company_logo_models)
            if res:
                logging.debug(f'linekdin_company_logo 更新影响: {res.rowcount=}')
        if link_company_base_info_id_models:
            res = self.tb_link_company_base_info_id_obj.save_data(link_company_base_info_id_models)
            if res:
                logging.debug(f'linekdin_company_base_info_id 更新影响: {res.rowcount=}')
        # 重新 匹配数据库数据 获取 匹配到的公司或人物 ID列表
        # 并更新存储 ES
        person_res = self.tb_link_person_list_obj.search_data_by_link_username(
            linkedin_username_list, self.es_person_fields
        )
        person_datalist = person_res.data if person_res else []
        company_res = self.tb_link_company_base_info_id_obj.search_data_by_job_company_id(
            job_company_id_list, self.es_company_fields
        )
        company_datalist = company_res.data if company_res else []
        if person_datalist:
            # 按解析顺序排序
            # 创建一个字典，用于快速查找排序索引
            order_index = {value: index for index, value in enumerate(linkedin_username_list)}
            # 按照 ID 的顺序排序 datalist
            person_datalist = sorted(person_datalist, key=lambda x: order_index.get(x["linkedin_username"], float('inf')))
            # 更新到 ES
            res = self.idx_linkedin_person_obj.bulk_import_to_es(person_datalist, uniq_col_name='id')
            logging.debug(f'link人物更新至 ES完成; {bool(res)}')
        if company_datalist:
            # 按解析顺序排序
            # 创建一个字典，用于快速查找排序索引
            order_index = {value: index for index, value in enumerate(job_company_id_list)}
            # 按照 ID 的顺序排序 datalist
            company_datalist = sorted(company_datalist,
                                     key=lambda x: order_index.get(x["job_company_id"], float('inf')))
            # 更新到 ES
            res = self.idx_linkedin_company_obj.bulk_import_to_es(company_datalist, uniq_col_name='id')
            logging.debug(f'link公司更新至 ES完成; {bool(res)}')
        return person_datalist, company_datalist

    def process_person_info_page(self, html_str: str) -> Tuple[List[dict], List[dict]]:
        """
        处理 人物详情页
        """
        if 'class="nav__link-person papabear:hidden mamabear:hidden"' in html_str:
            # 未登录模式
            # 解析 人物详情未登录页
            human_data = self.link_parser_obj.not_login_human_info_parse_obj.parse_data(html_str)
            # 解析人物基础信息
            link_person_list_model = self.no_login_clean_human_data_obj.clean_human_data(human_data)
            if not link_person_list_model.linkedin_username:
                return [], []
            linkedin_username_list = [
                link_person_list_model.linkedin_username
            ]
            res = self.tb_link_person_list_obj.search_data_by_link_username(linkedin_username_list)
            lid = res.data[0]['lid'] if res and res.data else link_person_list_model.lid
            link_person_list_model.lid = lid
            # 解析工作经历
            link_person_experience_model_list, company_logo_list = self.no_login_clean_human_data_obj.clean_link_person_experience(
                work_experience_list=human_data['work_experience'],
                human_name=link_person_list_model.full_name,
                lid=lid
            )
            job_company_id_list = [company_model.job_company_id for company_model in link_person_experience_model_list]
            # 解析教育经历
            link_person_education_model_list = self.no_login_clean_human_data_obj.clean_link_person_education(
                education_list=human_data['educational_experience'],
                human_name=link_person_list_model.full_name,
                lid=lid
            )
            # 解析头像
            link_person_logo_model = self.no_login_clean_human_data_obj.clean_link_person_logo(
                human_data=human_data,
                lid=lid
            )

            res = self.tb_link_person_list_obj.save_data(data=[link_person_list_model])
            if res:
                logging.info(f'未登录状态 人物详情新增/修改了{res.rowcount}条')
            res = self.tb_link_person_education_obj.save_data(data=link_person_education_model_list)
            if res:
                logging.info(f'未登录状态 人物教育经历新增/修改了{res.rowcount}条')
            res = self.tb_link_person_experience_obj.save_data(data=link_person_experience_model_list)
            if res:
                logging.info(f'未登录状态 人物工作经历新增/修改了{res.rowcount}条')
            res = self.tb_link_company_logo_obj.save_data(data=company_logo_list)
            if res:
                logging.info(f'未登录状态 公司logo新增/修改了{res.rowcount}条')
            if link_person_logo_model.logo_url:
                res = self.tb_link_person_logo_obj.save_data(data=[link_person_logo_model])
                if res:
                    logging.info(f'未登录状态 人物logo修改了{res.rowcount}条')
            else:
                logging.info(f'未登录状态 linkedin_username:{link_person_list_model.linkedin_username} 没有人物头像')
        elif '/voyager/api/me' in html_str:
            # 登录模式
            human_data = self.link_parser_obj.login_human_info_parse_obj.parse_data(html_str=html_str)
            if not human_data.get('human_name'):
                return [], []
            person_data,person_logo_url_data,education_data_list,experience_data_list,company_logo_list = self.login_clean_human_data_obj.clean_human_data(human_data)
            if not person_data or not person_data.get('linkedin_username'):
                return [], []

            job_company_name_list_dict = dict()
            job_company_name_list = [
                (data.get('job_company_name'),data.get('country_ios_code') or '') for data in experience_data_list
            ]
            job_company_linkedin_id_list = [
                data.get('job_company_linkedin_id') for data in experience_data_list if data.get('job_company_linkedin_id')
            ]
            job_company_name_list_res = self.tb_link_company_base_info_id_obj.search_data_by_job_company_name_and_country_code(job_company_name_list)
            job_company_name_list_res = job_company_name_list_res.data if job_company_name_list_res and job_company_name_list_res.data else []
            job_company_linkedin_id_list_res = self.tb_link_company_base_info_id_obj.search_job_company_id_by_job_company_linkedin_id(job_company_linkedin_id_list)
            job_company_linkedin_id_list_res = job_company_linkedin_id_list_res.data if job_company_linkedin_id_list_res and job_company_linkedin_id_list_res.data else []
            for d in job_company_name_list_res:
                job_company_name_list_dict[(d.get('job_company_name'), d.get('country_ios_code') or '')] = d.get('job_company_id')
            for d1 in job_company_linkedin_id_list_res:
                job_company_name_list_dict[d1.get('job_company_linkedin_id')] =  d1.get('job_company_id')
            for mod in experience_data_list:
                if job_company_name_list_dict.get((mod.get('job_company_name'), mod.get('country_ios_code'))) or job_company_name_list_dict.get(mod.get('job_company_linkedin_id')):
                    mod['job_company_id'] = job_company_name_list_dict.get(mod.get('job_company_linkedin_id')) or job_company_name_list_dict.get((mod.get('job_company_name'), mod.get('country_ios_code'))) or ''
                else:
                    value = mod.get('job_company_name') + mod.get('country_ios_code')
                    mod['job_company_id'] = hashlib.md5(value.encode('utf-8')).hexdigest()
            company_base_model_list = [
               LinkCompanyBaseInfoId(
                   job_company_name=data.get('job_company_name'),
                   job_company_id=data.get('job_company_id'),
                   job_company_linkedin_url=data.get('job_company_linkedin_url'),
                   job_company_linkedin_id=data.get('job_company_linkedin_id'),
                   job_company_location_name=data.get('job_company_location_name'),
                   job_company_location_locality=data.get('job_company_location_locality'),
                   job_company_location_region=data.get('job_company_location_region'),
                   job_company_location_country=data.get('job_company_location_country'),
                   country_ios_code=data.get('country_ios_code')
               ) for data in experience_data_list if data.get('job_company_id')
            ]

            if experience_data_list:
                job_last_updated_date = datetime.datetime.now()
                job_last_updated = str(job_last_updated_date.year) + '-' + str('{:0>2d}'.format(job_last_updated_date.month)) + '-' + str('{:0>2d}'.format(job_last_updated_date.day))
                person_data['job_last_updated'] = job_last_updated
                person_data['job_title'] = experience_data_list[0].get('title_name') or ''
                person_data['job_start_date'] = experience_data_list[0].get('start_date') or ''
                person_data['job_company_id'] = experience_data_list[0].get('job_company_id') or ''
                person_data['job_company_name'] = experience_data_list[0].get('job_company_name') or ''
            link_person_list_model = LinkPersonList(**person_data)
            linkedin_username = link_person_list_model.linkedin_username
            linkedin_username_list = [
                linkedin_username
            ]
            res = self.tb_link_person_list_obj.search_data_by_link_username(linkedin_username_list)
            lid = res.data[0]['lid'] if res and res.data else link_person_list_model.lid
            link_person_list_model.lid = lid
            experience_model_list = [
                LinkPersonExperience(lid=link_person_list_model.lid, full_name=link_person_list_model.full_name,**data) for data in experience_data_list if data.get('job_company_id')
            ]
            res = self.tb_link_person_list_obj.save_data(data=[link_person_list_model])
            if res:
                logging.info(f'登录状态下,人物更新了{res.rowcount}条')
            if person_logo_url_data.get('logo_url'):
                link_person_logo_model = LinkPersonLogo(lid=lid,logo_url=person_logo_url_data.get('logo_url'))
                res = self.tb_link_person_logo_obj.save_data(data=[link_person_logo_model])
                if res:
                    logging.info(f'登录状态 人物logo修改了{res.rowcount}条')
            else:
                logging.info(f'登录状态 linkedin_username:{linkedin_username} 没有人物头像')

            job_company_id_list = [models.job_company_id for models in company_base_model_list]
            res = self.tb_link_company_base_info_id_obj.save_data(company_base_model_list)
            if res:
                logging.info(f'登录状态下,link_username:{linkedin_username} 更新了{res.rowcount}条公司')

            res = self.tb_link_person_experience_obj.save_data(experience_model_list)
            if res:
                logging.info(f'登录状态下,link_username:{linkedin_username} 更新了{res.rowcount}条工作经历')

            company_logo_list = [
                LinkCompanyLogo(**logo_model) for logo_model in company_logo_list
            ]
            res = self.tb_link_company_logo_obj.save_data(company_logo_list)
            if res:
                logging.info(f'登录状态下,link_username:{link_person_list_model.linkedin_username} 更新了{res.rowcount}条公司logo')

            res_data = self.get_res_data(linkedin_username)
            lid, full_name = res_data.get('lid'), res_data.get('full_name')

            education_model_list = []
            linkedin_id_list = list()
            school_name_list = list()
            sid_dict = dict()

            for education in education_data_list:
                education_model = LinkPersonEducation(
                    lid=lid,
                    full_name=full_name,
                    **education
                )
                education_model_list.append(education_model)
                if education_model.linkedin_id:
                    linkedin_id_list.append(education_model.linkedin_id)
                if education_model.school_name:
                    school_name_list.append(education_model.school_name.lower())
            select_school_linkedin_id_obj = self.tb_link_person_education_obj.select_education_data_by_linkedin_id(linkedin_id_list)
            select_school_linkedin_id_obj = select_school_linkedin_id_obj.data if select_school_linkedin_id_obj else []
            select_school_name_obj = self.tb_link_person_education_obj.select_education_data_by_school(school_name_list)
            select_school_name_obj = select_school_name_obj.data if select_school_name_obj else []
            for school_linkedin_id in select_school_linkedin_id_obj:
                if school_linkedin_id.get('linkedin_id'):
                    sid_dict[school_linkedin_id.get('linkedin_id')] = school_linkedin_id.get('sid', '')
            for school_linkedin_id in select_school_name_obj:
                if school_linkedin_id.get('school_name'):
                    sid_dict[school_linkedin_id.get('school_name').lower()] = school_linkedin_id.get('sid', '')
            for education_model in education_model_list:
                init_sid = sid_dict.get(education_model.linkedin_id) or sid_dict.get(education_model.school_name.lower())
                if init_sid:
                    education_model.sid = init_sid
            res = self.tb_link_person_education_obj.save_data(education_model_list)
            if res:
                logging.info(f'人物 like_username:{linkedin_username} 教育经历更新/插入 {res.rowcount}条数据')
        else:
            linkedin_username_list = []
            job_company_id_list = []

        # 重新 匹配数据库数据 获取 匹配到的公司或人物 ID列表
        # 并更新存储 ES
        if linkedin_username_list:
            person_res = self.tb_link_person_list_obj.search_data_by_link_username(
                linkedin_username_list, fields=self.es_person_fields
            )
            person_data_list = person_res.data if person_res else []
        else:
            person_data_list = []
        if job_company_id_list:
            company_res = self.tb_link_company_base_info_id_obj.search_data_by_job_company_id(
                job_company_id_list, fields=self.es_company_fields
            )
            company_data_list = company_res.data if company_res else []
        else:
            company_data_list = []

        if person_data_list:
            # 更新到 ES
            res = self.idx_linkedin_person_obj.bulk_import_to_es(person_data_list, uniq_col_name='id')
            logging.debug(f'link人物更新至 ES完成; {bool(res)}')
        if company_data_list:
            # 更新到 ES
            res = self.idx_linkedin_company_obj.bulk_import_to_es(company_data_list, uniq_col_name='id')
            logging.debug(f'link公司更新至 ES完成; {bool(res)}')

        return person_data_list, company_data_list

    def process_person_contact_page(self, html_str: str)-> Tuple[List[dict], List[dict]]:
        """
        处理人物详情页的联系方式
        """
        if '/voyager/api/me' in html_str:
            human_data = self.link_parser_obj.login_human_info_parse_obj.parse_data(html_str=html_str)
            if not human_data.get('human_name'):
                return [], []
            contact_data = self.login_clean_human_data_obj.clean_human_contact(human_data)

            link_person_contact_model = LinkPersonList(**contact_data)

            linkedin_username = link_person_contact_model.linkedin_username
            linkedin_username_list = [
                linkedin_username
            ]

            res = self.tb_link_person_list_obj.search_data_by_link_username(linkedin_username_list)
            lid = res.data[0]['lid'] if res and res.data else link_person_contact_model.lid
            link_person_contact_model.lid = lid

            res = self.tb_link_person_list_obj.save_data(data=[link_person_contact_model])
            if res:
                logging.info(f'登录状态下,人物更新了{res.rowcount}条')
        else:
            linkedin_username_list = []

        # 重新 匹配数据库数据 获取 匹配到的公司或人物 ID列表
        # 并更新存储 ES
        if linkedin_username_list:
            person_res = self.tb_link_person_list_obj.search_data_by_link_username(
                linkedin_username_list, fields=self.es_person_fields
            )
            person_data_list = person_res.data if person_res else []
        else:
            person_data_list = []

        if person_data_list:
            # 更新到 ES
            res = self.idx_linkedin_person_obj.bulk_import_to_es(person_data_list, uniq_col_name='id')
            logging.debug(f'link人物更新至 ES完成; {bool(res)}')

        return person_data_list, []


    def process_company_info_page(self, html_str: str) -> Tuple[List[dict], List[dict]]:
        """
        处理 公司详情页
        """
        if "nav__button-secondary" in html_str:
            company_data = self.link_parser_obj.not_login_company_parse_obj.parse_data(html_str)
            company_base_info_model, logo_model, human_list = self.no_login_clean_company_obj.clean_company_data(company_data)
            if not company_base_info_model.job_company_id or not company_base_info_model.job_company_name:
                return [], []
            job_company_id_list = [
                company_base_info_model.job_company_id
            ]
            human_linkedin_username_list = [
                human_model.linkedin_username for human_model in human_list
            ]
            res = self.tb_link_company_base_info_id_obj.save_data(data=[company_base_info_model])
            if res:
                logging.info(f'未登录公司详情更新/插入{res.rowcount}条')

            res = self.tb_link_company_logo_obj.save_data(data=[logo_model])
            if res:
                logging.info(f'未登录公司logo更新/插入{res.rowcount}条')
            res = self.tb_link_person_list_obj.save_data(data=human_list)
            if res:
                logging.info(f'未登录公司高管人物更新/插入{res.rowcount}条')
        elif "/voyager/api/me" in html_str:
            company_data = self.link_parser_obj.login_company_parse_obj.parse_data(html_str)
            company_data, company_logo_list = self.login_clean_company_obj.clean_company_data(company_data)
            if not company_data.get('job_company_id') or not company_data.get('job_company_name'):
                return [], []
            company_data_model = LinkCompanyBaseInfoId(**company_data)
            job_company_id_list = [
                company_data_model.job_company_id
            ]
            company_logo_list = [
                LinkCompanyLogo(**logo_model) for logo_model in company_logo_list
            ]
            res = self.tb_link_company_base_info_id_obj.save_data([company_data_model])
            if res:
                logging.info(f'登录公司详情更新/插入{res.rowcount}条')
            res = self.tb_link_company_logo_obj.save_data(company_logo_list)
            if res:
                logging.info(f'登录公司logo更新/插入{res.rowcount}条')
            human_linkedin_username_list = []
        else:
            human_linkedin_username_list = []
            job_company_id_list = []

        person_res = self.tb_link_person_list_obj.search_data_by_link_username(
            human_linkedin_username_list, fields=self.es_person_fields
        )
        person_data_list = person_res.data if person_res else []
        company_res = self.tb_link_company_base_info_id_obj.search_data_by_job_company_id(
            job_company_id_list, fields=self.es_company_fields
        )
        company_data_list = company_res.data if company_res else []
        if person_data_list:
            # 更新到 ES
            res = self.idx_linkedin_person_obj.bulk_import_to_es(person_data_list, uniq_col_name='id')
            logging.debug(f'link人物更新至 ES完成; {bool(res)}')
        if company_data_list:
            # 更新到 ES
            res = self.idx_linkedin_company_obj.bulk_import_to_es(company_data_list, uniq_col_name='id')
            logging.debug(f'link公司更新至 ES完成; {bool(res)}')
        return person_data_list, company_data_list


    def get_res_data(self, linkedin_username):
        linkedin_username_list = [
            linkedin_username
        ]
        res = self.tb_link_person_list_obj.search_data_by_link_username(linkedin_username_list)
        res_data = res.data[0] if res and res.data else dict()
        return res_data


def test_unit():
    import parse

    # with open('../test/aa.json', 'r', encoding='utf-8') as f:
    #     json_data = json.load(f)
    with open('../test/aaa.json', 'r', encoding='utf-8') as f:
        html_data = f.read()

    # html_str = f.read()
    # Preprocessor().process_search_page(html_str)
    # region = 'US'
    # region = 'Hong Kong'
    # region = 'Kowloon'
    # area_data_info = Preprocessor().mate_area_data(region)
    # print(area_data_info)

    # print(Preprocessor().deal_graphql_experience(json_data))

    person_data_list, company_data_list = Preprocessor().process_person_info_page(html_data)
    print(person_data_list, company_data_list)


if __name__ == '__main__':
    test_unit()
