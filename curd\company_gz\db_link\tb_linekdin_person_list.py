from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult
from _models.company_gz.db_link import LinkPersonList
from typing import List, Union, Sequence, Optional, Dict


class TBLinekdinPersonList:
    db = 'db_link'
    table = 'linekdin_person_list'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql
        self.field_schema = self.db_mysql.read_column_default(self.table, self.db)

    def save_data(self, data: List[LinkPersonList], is_safe=True) -> Union[None, MysqlResult]:
        """
        存储更新数据
        """
        if not data:
            return
        if is_safe:
            field_schema = self.field_schema
        else:
            field_schema = None
        datalist = [d.model_dump() for d in data]
        res = self.db_mysql.save(self.table, datalist, db=self.db, field_schema=field_schema)
        return res

    def search_data_by_link_username(self, link_username_list: list, fields: list = None) -> Union[None, MysqlResult]:
        """
        通过 领英 链接 ID 查找数据
        """
        if not link_username_list:
            return None
        if not fields:
            fields = [
                "id", "lid", "linkedin_username", "linkedin_username_clean", "full_name"
            ]
        cols = ','.join(fields)
        bfhs = ','.join(['%s'] * len(link_username_list))
        sql = f'''
            select {cols} from {self.db}.{self.table} 
            where linkedin_username in ({bfhs}) or linkedin_username_clean in ({bfhs})
            order by id
        '''
        value = link_username_list + link_username_list
        res = self.db_mysql.execute(sql, value=value)
        return res


