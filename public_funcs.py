#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: CaiChen
@File: public_funcs.py
@Date: 2023/1/5 上午10:01
@Desc: 
"""
import json
import datetime
import decimal
import time
import copy

from django.shortcuts import HttpResponse
from asyncio.coroutines import iscoroutinefunction


class DateEncoder(json.JSONEncoder):
    """
    清除datetime类型不可json转换问题
    """

    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(obj, datetime.date):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        else:
            return json.JSONEncoder.default(self, obj)


def jsonDumpsData(data):
    """
    json转换
    """
    data = json.dumps(data, cls=DateEncoder, ensure_ascii=False)
    return data


def response_as_json(data, http_code=200):
    json_str = jsonDumpsData(data)
    response = HttpResponse(
        json_str,
        content_type="application/json",
        status=http_code,
    )
    response["Access-Control-Allow-Origin"] = "*"
    return response


def json_response(task_status: str, rs_data=None, msg="success", code=200, http_code=200, **kwargs):
    if rs_data is None:
        rs_data = {}
    
    rs_data["task_status"] = task_status
    data = {
        "code": code,
        "msg": msg,
        "data": rs_data
    }
    data.update(kwargs)
    return response_as_json(data, http_code=http_code)


def json_error(error_string="error", code=500, http_code=200, **kwargs):
    data = {
        "code": code,
        "msg": error_string,
        "data": {}
    }
    data.update(kwargs)
    return response_as_json(data, http_code=http_code)


def get_access_ip(request):
    """
    获取访问者IP地址
    """
    # x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    x_forwarded_for = request.META.get('HTTP_X_REAL_IP')  ## nginx代理设置的代理前真实IP
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]  # 这里是真实的ip
    else:
        ip = request.META.get('REMOTE_ADDR', '')  # 这里获得代理ip
    return ip


def security_restrictions(received_json_data):
    """
    安全限制
    """
    received_json_data_c = copy.deepcopy(received_json_data)
    page_size = received_json_data.get('pageSize', 20)
    page_no = received_json_data.get('pageNo', 1)
    if not page_size:
        page_size = 20
    if not page_no:
        page_no = 1
    all_num = int(page_size) * int(page_no)
    if all_num > 1000:
        page_size = 1
        page_no = 1
    received_json_data_c['pageSize'] = page_size
    received_json_data_c['pageNo'] = page_no
    return received_json_data_c


# 创建类方法 计时器
def timer(func):
    def inner(self, *args, **kwargs):
        s_time = time.perf_counter()
        res = func(self, *args, **kwargs)
        e_time = time.perf_counter()
        ip = get_access_ip(args)
        search_args = json.loads(args.body)
        search_args = json.dumps(search_args, ensure_ascii=False)
        func_name = func.__name__
        time_consuming = e_time - s_time
        print(f'{self.__class__.__name__}.{func_name} === time consuming: {time_consuming} s')
        return res

    async def func_async(self, *args, **kwargs):
        s_time = time.perf_counter()
        res = func(self, *args, **kwargs)
        e_time = time.perf_counter()
        func_name = func.__name__
        time_consuming = e_time - s_time
        print(f'{self.__class__.__name__}.{func_name} === time consuming: {time_consuming} s')
        return res

    if iscoroutinefunction(func):
        return func_async
    else:
        return inner


JsonResponse = json_response
JsonError = json_error
