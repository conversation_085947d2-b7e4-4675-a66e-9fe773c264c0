from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult
from _models.company_gz.db_link import LinkPersonExperience
from typing import List, Union


class TBLinekdinPersonExperience:
    db = 'db_link'
    table = 'linekdin_person_experience'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql
        self.field_schema = self.db_mysql.read_column_default(self.table, self.db)

    def save_data(self, data: List[LinkPersonExperience], is_safe=True) -> Union[None, MysqlResult]:
        """
        存储更新数据
        """
        if not data:
            return
        if is_safe:
            field_schema = self.field_schema
        else:
            field_schema = None
        datalist = [d.model_dump() for d in data]
        res = self.db_mysql.save(self.table, datalist, db=self.db, field_schema=field_schema)
        return res

