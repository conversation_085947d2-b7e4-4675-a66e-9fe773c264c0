from typing import List, Dict
from common.database import ElasticSearch
from settings import *


class IdxLinkedinCompany:
    index_name = "linkedin_company"

    def __init__(self):
        self.es_client_obj = ElasticSearch(ES_USER, ES_PWD)

    # 批量导入到 Elasticsearch
    def bulk_import_to_es(self, datalist: List[Dict], uniq_col_name: str):
        # 选择一个 Elasticsearch 节点进行操作
        es_url = ES_NODES[0]  # 可以实现轮询或其他负载均衡逻辑

        es_bulk_url = f'{es_url}/{self.index_name}/_bulk'
        headers = {'Content-Type': 'application/json'}
        res = self.es_client_obj.bulk_import_to_es(
            es_bulk_url, datalist, uniq_col_name=uniq_col_name, headers=headers
        )
        return res
