import json
import re
import urllib.parse
from lxml import etree
from public.parse_funcs import custom_unescape


class NotLoginHumanInfoParse:
    """
    未登录人物详情解析类
    """

    def __init__(self):
        self.human_name_xpath = './/h1[@class="top-card-layout__title font-sans text-lg papabear:text-xl font-bold leading-open text-color-text mb-0"]//text()'
        self.human_occupation_xpath = './/h2[@class="top-card-layout__headline break-words font-sans text-md leading-open text-color-text"]//text()'
        self.region_xpath = '(.//h3[@class="top-card-layout__first-subline font-sans text-md leading-open text-color-text-low-emphasis"]/div[@class="profile-info-subheader"]/div[@class="not-first-middot"])[1]/span//text()'
        self.current_company_xpath = './/div[@class="top-card__links-container"]/div[@data-section="currentPositionsDetails"]/a/span//text()'
        self.current_company_url_xpath = './/div[@class="top-card__links-container"]/div[@data-section="currentPositionsDetails"]/a/@href'
        self.current_company_logo_url_xpath = './/div[@class="top-card__links-container"]/div[@data-section="currentPositionsDetails"]/a/img/@src'
        self.graduating_institution_xpath = './/div[@class="top-card__links-container"]/div[@data-section="educationsDetails"]/a/span/text()'
        self.graduating_institution_logo_xpath = './/div[@class="top-card__links-container"]/div[@data-section="educationsDetails"]/a/img/@src'
        self.followers_and_friends_xpath = '(.//h3[@class="top-card-layout__first-subline font-sans text-md leading-open text-color-text-low-emphasis"]/div[@class="profile-info-subheader"]/div[@class="not-first-middot"])/span/text()'
        self.all_work_experience_xpath = '(.//ul[@class="experience__list"]/li)'    # 完整的工作经历以及教育经历需要登录才能看到
        self.all_educational_experience_xpath = '(.//ul[@class="education__list"]/li)'
        self.all_language_ability_xpath = '(.//section[@data-section="languages"]/div[@class="core-section-container__content break-words"]/ul/li)'
        self.human_id_xpath = '(.//ul[contains(concat(" ", normalize-space(@class), " "), " show-more-less__list ")]/li)'
        self.linkedin_username_xpath = './/link[@rel="canonical"]/@href'  # 人物id xpath
        self.json_xpath = './/script[@type="application/ld+json"]//text()'

    def parse_human_base_info(self, html_obj):
        """解析人物基本信息"""
        human_data = {'country_code': '', 'human_avatar': ''}
        linker_json_data = html_obj.xpath('.//script[@type="application/ld+json"]/text()')
        if linker_json_data and type(json.loads(linker_json_data[0])) == dict:
            linker_json_data = json.loads(linker_json_data[0])
            for data in linker_json_data.get('@graph', []):
                if data['@type'] == 'Person':
                    human_data['country_code'] = data.get('address', {}).get('addressCountry', '')
                    human_data['human_avatar'] = data.get('image', {}).get('contentUrl', '')
                    break
        human_name = self.processing_data(html_obj, self.human_name_xpath)
        human_id = self.processing_data(html_obj, self.linkedin_username_xpath)  # 领英标识
        human_id = human_id[0] if human_id else ''
        human_host = 'https://dk.linkedin.com/in/'
        human_id_index = human_id.find(human_host)
        human_id = human_id[human_id_index + len(human_host):] if human_id_index >= 0 else ''
        human_occupation = self.processing_data(html_obj, self.human_occupation_xpath)
        current_company = self.processing_data(html_obj, self.current_company_xpath)
        current_company_id = self.processing_data(html_obj, self.current_company_url_xpath)
        current_company_logo_url = self.processing_data(html_obj, self.current_company_logo_url_xpath)
        graduating_institution = self.processing_data(html_obj, self.graduating_institution_xpath)
        graduating_institution_logo = self.processing_data(html_obj, self.graduating_institution_logo_xpath)
        region = self.processing_data(html_obj, self.region_xpath)
        followers_and_friends = ''.join(self.processing_data(html_obj, self.followers_and_friends_xpath))
        followers_friends_pat = re.compile(r'(.*?)followers(.*?)connections')
        number_of_followers_friends = followers_friends_pat.findall(followers_and_friends)
        try:
            if len(number_of_followers_friends) > 0 and len(number_of_followers_friends[0]) > 0:
                number_of_friends = number_of_followers_friends[0][0]
                if 'K' in number_of_friends:
                    number_of_friends = number_of_friends.replace('K', '')
                    number_of_friends = int(number_of_friends) * 10 ** 3
                elif 'W' in number_of_friends:
                    number_of_friends = number_of_friends.replace('W', '')
                    number_of_friends = int(number_of_friends) * 10 ** 4
                else:
                    number_of_friends = int(number_of_friends)
            else:
                number_of_friends = 0
        except:
            number_of_friends = 0
        human_data['number_of_friends'] = number_of_friends  # 好友数
        try:
            if len(number_of_followers_friends) > 0 and len(number_of_followers_friends[0]) > 1:
                number_of_followers = number_of_followers_friends[0][1]
                if 'K' in number_of_followers:
                    number_of_followers = number_of_followers.replace('K', '')
                    number_of_followers = int(number_of_followers * 10 ** 3)
                elif 'W' in number_of_followers:
                    number_of_followers = number_of_followers.replace('W', '')
                    number_of_followers = int(number_of_followers * 10 ** 4)
                else:
                    number_of_followers = int(number_of_followers)
            else:
                number_of_followers = 0
        except:
            number_of_followers = 0  # 关注数
        human_data['human_name'] = human_name[0] if human_name else ''  # 人物名称
        human_data['human_id'] = human_id
        human_data['human_occupation'] = human_occupation[0] if human_occupation else ''  # 人物职业
        human_data['current_company'] = current_company[0] if current_company else ''  # 当前任职公司
        current_company_id = current_company_id[0] if current_company_id else ''
        current_company_id = re.findall('/company/(.*?)\?', current_company_id)
        human_data['current_company_id'] = urllib.parse.unquote(
            current_company_id[0]) if current_company_id else ''  # 当前任职公司id
        human_data['current_company_logo_url'] = current_company_logo_url[
            0] if current_company_logo_url else ''  # 当前任职公司logo的url
        human_data['graduating_institution'] = graduating_institution[0] if graduating_institution else ''  # 毕业院校
        human_data['graduating_institution_logo'] = graduating_institution_logo[
            0] if graduating_institution_logo else ''  # 毕业院校logo的url
        human_data['region'] = region[0] if region else ''  # 人物所属地区
        human_data['number_of_followers'] = number_of_followers  # 关注数
        human_data['number_of_friends'] = number_of_friends  # 好友数
        return human_data

    def parse_work_experience(self, html_obj):
        """
        解析人物工作经历
        """

        all_work_experience_list = html_obj.xpath(self.all_work_experience_xpath)
        work_experience = []
        for work_experience_index in range(len(all_work_experience_list)):
            item_xpath = f'{self.all_work_experience_xpath}[{work_experience_index + 1}]'
            item_class_data = html_obj.xpath(f'{item_xpath}/@class')
            item_class_data = item_class_data[0] if item_class_data else ''
            work_experience_data = {

                'company_name': '',  # 公司名称
                'company_id': '',  # 公司url
                'company_logo_url': '',  # 公司logo的url
                'total_duration': '',  # 总时长
                'work_experience': []  # 工作经历
            }
            if item_class_data == "profile-section-card relative flex w-full list-none py-1.5 pr-2 pl-1 experience-item":
                # 公司名称
                company_name_xpath = f'{item_xpath}/div[@class="pl-0.5 grow break-words"]/h4[@class="text-color-text text-md [&>*]:mb-0 not-first-middot leading-[1.75]"]//text()'
                company_name = self.processing_data(html_obj, company_name_xpath)
                company_name = company_name[0] if company_name else ''
                # 公司id
                company_url_xpath = f'{item_xpath}/a/@href'
                company_url = self.processing_data(html_obj, company_url_xpath)
                company_url = company_url[0] if company_url else ''
                company_id = re.findall('/company/(.*?)\?', company_url)
                company_id = company_id[0] if company_id else ''
                # 公司logo的url
                company_logo_url_xpath = f'{item_xpath}/a/img/@data-delayed-url'
                company_logo_url = self.processing_data(html_obj, company_logo_url_xpath)
                company_logo_url = company_logo_url[0] if company_logo_url else ''
                # 总时长
                total_duration_xpath = f'({item_xpath}/div[@class="pl-0.5 grow break-words"]/div[@class="text-color-text-low-emphasis text-md [&>*]:mb-0 [&>*]:text-md [&>*]:text-color-text-low-emphasis"]/p)[1]/span//text()'
                total_duration = self.processing_data(html_obj, total_duration_xpath)
                total_duration = total_duration[:-1] + ['·'] + total_duration[
                                                               -1:] if total_duration and '·' not in total_duration else total_duration
                total_duration = ' '.join(total_duration)
                # 工作地点
                work_location_xpath = f'({item_xpath}/div[@class="pl-0.5 grow break-words"]/div[@class="text-color-text-low-emphasis text-md [&>*]:mb-0 [&>*]:text-md [&>*]:text-color-text-low-emphasis"]/p)[2]//text()'
                work_location = self.processing_data(html_obj, work_location_xpath)
                work_location = work_location[0] if work_location else ''
                # 岗位名称
                job_title_xpath = f'{item_xpath}/div[@class="pl-0.5 grow break-words"]/h3[@class="[&>*]:mb-0 text-[18px] text-color-text leading-regular group-hover:underline font-semibold"]//text()'
                job_title = self.processing_data(html_obj, job_title_xpath)
                job_title = job_title[0] if job_title else ''
                # 工作经历
                work_experience_obj = {
                    'job_title': job_title,  # 岗位名称
                    'duration_of_employment': total_duration,  # 任职时长
                    'work_location': work_location  # 工作地点
                }
                work_experience_data['company_name'] = company_name
                work_experience_data['company_id'] = urllib.parse.unquote(company_id)
                work_experience_data['company_logo_url'] = company_logo_url
                work_experience_data['total_duration'] = total_duration
                work_experience_data['work_experience'].append(work_experience_obj)
                if company_name:
                    work_experience.append(work_experience_data)
            elif item_class_data == "experience-group":
                # 公司名称
                company_name_xpath = f'{item_xpath}/a[@data-tracking-control-name="public_profile_experience-group-header"]/@title'
                company_name = self.processing_data(html_obj, company_name_xpath)
                company_name = company_name[0] if company_name else ''
                # 总时长
                total_duration_xpath = f'{item_xpath}/a[@data-tracking-control-name="public_profile_experience-group-header"]/div[@class="experience-group-header flex pb-1.5"]/div[@class="pl-1"]/p/span//text()'
                total_duration = self.processing_data(html_obj, total_duration_xpath)
                total_duration = ' '.join(total_duration)
                # 公司url
                company_url_xpath = f'{item_xpath}/a/@href'
                company_url = self.processing_data(html_obj, company_url_xpath)
                company_url = company_url[0] if company_url else ''
                company_id = re.findall('/company/(.*?)\?', company_url)
                company_id = company_id[0] if company_id else ''
                # 公司logo的url
                company_logo_url_xpath = f'({item_xpath}/a/div[@class="experience-group-header flex pb-1.5"]/div)[1]/img/@data-delayed-url'
                company_logo_url = self.processing_data(html_obj, company_logo_url_xpath)
                company_logo_url = company_logo_url[0] if company_logo_url else ''
                work_experience_data['company_name'] = company_name
                work_experience_data['company_id'] = urllib.parse.unquote(company_id)
                work_experience_data['company_logo_url'] = company_logo_url
                work_experience_data['total_duration'] = total_duration
                # 工作经历
                work_experience_children_xpath = f'({item_xpath}/ul/li[@data-section="currentPositionsDetails"])'
                work_experience_children_items = html_obj.xpath(work_experience_children_xpath)
                for work_experience_children_index in range(len(work_experience_children_items)):
                    # 岗位名称
                    job_title_xpath = f'{work_experience_children_xpath}[{work_experience_children_index + 1}]/div[@class="pl-0.5 grow break-words"]/h3/span//text()'
                    job_title = self.processing_data(html_obj, job_title_xpath)
                    job_title = job_title[0] if job_title else ''
                    # 任职时长
                    duration_of_employment_xpath = f'({work_experience_children_xpath}[{work_experience_children_index + 1}]/div[@class="pl-0.5 grow break-words"]/div[@class="text-color-text-low-emphasis text-md [&>*]:mb-0 [&>*]:text-md [&>*]:text-color-text-low-emphasis"]/p)[1]/span//text()'
                    duration_of_employment = self.processing_data(html_obj, duration_of_employment_xpath)
                    duration_of_employment = duration_of_employment[:-1] + ['·'] + duration_of_employment[
                                                                                   -1:] if duration_of_employment and '·' not in duration_of_employment else duration_of_employment
                    duration_of_employment = ' '.join(duration_of_employment)
                    # 工作地点
                    work_location_xpath = f'({work_experience_children_xpath}[{work_experience_children_index + 1}]/div[@class="pl-0.5 grow break-words"]/div[@class="text-color-text-low-emphasis text-md [&>*]:mb-0 [&>*]:text-md [&>*]:text-color-text-low-emphasis"]/p)[2]//text()'
                    work_location = self.processing_data(html_obj, work_location_xpath)
                    work_location = work_location[0] if work_location else ''
                    # 工作经历
                    work_experience_obj = {
                        'job_title': job_title,  # 岗位名称
                        'duration_of_employment': duration_of_employment,  # 任职时长
                        'work_location': work_location  # 工作地点
                    }
                    work_experience_data['work_experience'].append(work_experience_obj)
                if company_name:
                    work_experience.append(work_experience_data)
        return work_experience

    def parse_educational_experience(self, html_obj):
        """解析教育经历"""

        all_educational_experience_item = html_obj.xpath(self.all_educational_experience_xpath)
        educational_experience = []
        for educational_experience_index in range(len(all_educational_experience_item)):
            educational_experience_item_xpath = f'{self.all_educational_experience_xpath}[{educational_experience_index + 1}]'
            school_name_xpath = f'{educational_experience_item_xpath}/div[@class="pl-0.5 grow break-words"]/h3//text()'
            study_major_xpath = f'{educational_experience_item_xpath}/div[@class="pl-0.5 grow break-words"]/h4[@class="text-color-text text-md [&>*]:mb-0 not-first-middot leading-[1.75]"]/span//text()'
            learning_duration_xpath = f'{educational_experience_item_xpath}/div[@class="pl-0.5 grow break-words"]/div[@class="text-color-text-low-emphasis text-md [&>*]:mb-0 [&>*]:text-md [&>*]:text-color-text-low-emphasis"]/p/span//text()'
            details_xpath = f'{educational_experience_item_xpath}/div[@class="pl-0.5 grow break-words"]/div[@class="text-color-text-low-emphasis text-md [&>*]:mb-0 [&>*]:text-md [&>*]:text-color-text-low-emphasis"]/div[@class="control-transition"]/div[@class="show-more-less-text"]/p//text()'
            school_url_xpath = f'{educational_experience_item_xpath}/a/@href'

            # school_name_list = self.processing_data(html_obj, school_name_xpath)
            school_name_list = html_obj.xpath(school_name_xpath)
            # study_major_list = self.processing_data(html_obj, study_major_xpath)
            study_major_list = html_obj.xpath(study_major_xpath)
            # learning_duration_list = self.processing_data(html_obj, learning_duration_xpath)
            learning_duration_list = html_obj.xpath(learning_duration_xpath)
            # details_list = self.processing_data(html_obj, details_xpath)
            details_list = html_obj.xpath(details_xpath)
            school_url = self.processing_data(html_obj, school_url_xpath)
            school_url = school_url[0] if school_url else ''
            school_id = re.findall(r'school/(.*?)/\?', school_url)
            school_id = school_id[0] if school_id else ''
            linkedin_url = f'linkedin.com/school/{school_id}' if school_id else ''

            school_name = ' '.join(school_name_list) if school_name_list else ''
            study_major = ','.join(study_major_list)
            learning_duration = ''.join(learning_duration_list)
            details = '\n'.join(details_list)

            educational_experience_obj = {
                'school_name': school_name.strip().replace('\n', ''),  # 学校名称
                'study_major': study_major,  # 学习专业
                'learning_duration': learning_duration.strip().replace('\n', ''),  # 学习时长
                'details': details.strip().replace('\n', '').replace('\t', ''),  # 详情
                'school_id': urllib.parse.unquote(school_id),  # 学校id
                'linkedin_url': linkedin_url
            }
            educational_experience.append(educational_experience_obj)
        return educational_experience

    def parse_language_ability(self, html_obj):
        """解析语言能力"""
        language_ability = []
        all_language_ability_items = html_obj.xpath(self.all_language_ability_xpath)
        for language_ability_index in range(len(all_language_ability_items)):
            item_xpath = f'{self.all_language_ability_xpath}[{language_ability_index + 1}]'
            language_name_xpath = f'{item_xpath}/div[@class="pl-0.5 grow break-words"]/h3[@class="[&>*]:mb-0 text-[18px] text-color-text leading-regular group-hover:underline font-semibold"]//text()'
            language_leval_xpath = f'{item_xpath}/div[@class="pl-0.5 grow break-words"]/h4[@class="text-color-text text-md [&>*]:mb-0 not-first-middot leading-[1.75]"]//text()'
            language_name = self.processing_data(html_obj, language_name_xpath)
            language_name = language_name[0] if language_name else ''
            language_leval = self.processing_data(html_obj, language_leval_xpath)
            language_leval = language_leval[0] if language_leval else ''
            language_data_obj = {
                'language_name': language_name,
                'language_leval': language_leval
            }
            language_ability.append(language_data_obj)
        return language_ability

    def parse_similar_profiles(self, html_obj):
        """解析相似推荐人物"""
        human_id_items = html_obj.xpath(self.human_id_xpath)
        human_id_data_list = []
        for human_id_index in range(len(human_id_items)):
            # 人物ID
            human_url_xpath = f'({self.human_id_xpath}[{human_id_index + 1}]/a)[1]/@href'
            human_url = self.processing_data(html_obj, human_url_xpath)
            human_url = human_url[0] if human_url else ''
            human_id = re.findall(r'linkedin\.com/in/(.*?)\?', human_url)
            human_id = human_id[0] if human_id else ''
            if human_id:
                human_id_data_obj = {
                    'human_id': urllib.parse.unquote(human_id),  # 人物ID
                }
                human_id_data_list.append(human_id_data_obj)
        return human_id_data_list

    def get_json_data(self, html_obj):
        json_data = html_obj.xpath(self.json_xpath)
        return json.loads(json_data[0]) if json_data else {}

    def parse_data(self, response_str):
        html_obj = etree.HTML(response_str)
        try:
            human_data = self.get_json_data(html_obj)
            graph_data = human_data.get("@graph", [])

            if isinstance(graph_data, list) and len(graph_data) >= 2:
                human_data = {}
                for item in graph_data:
                    if isinstance(item, dict):
                        human_data.update(item)
            else:
                human_data = human_data.get("@graph", [])[0]
            
        except:
            human_data = dict()
        """
        工作经历以及教育经历需要登录才能查看
        """
        # 工作经历
        human_data['work_experience'] = self.parse_work_experience(html_obj)
        # 教育经历
        educational_experience = self.parse_educational_experience(html_obj)
        human_data['educational_experience'] = educational_experience
        # 语言能力
        language_ability = self.parse_language_ability(html_obj)
        human_data['language_ability'] = language_ability
        return human_data

    def processing_data(self, html_data, xpath_str):
        v_entities = {
            '\n': '',
            '\r': '',
            '\t': '',
            '\u3000': '',
            '\xa0': '',

        }
        return [custom_unescape(value, v_entities).strip() for value in html_data.xpath(xpath_str)
                if custom_unescape(value, v_entities).strip()]

    def test_unit(self):
        with open('../test/test.html', 'r', encoding='utf-8') as f:
            html_str = f.read()
        html_str = custom_unescape(html_str)
        human_data = self.parse_data(html_str)

        return human_data


class LoginHumanParse:

    def __init__(self):
        self.json_data_xpath = './/code[contains(@id,"bpr-guid-") and not(contains(@id,"datalet"))]//text()'
        self.human_name_pat = re.compile(r'/in/(.*?)/overlay/')

    def parse_data(self, html_str):
        html_str1 = custom_unescape(html_str)
        human_name = self.human_name_pat.findall(html_str1)
        human_name = human_name[0] if human_name else ''
        data_obj = {
            'html_str': html_str1,
            'human_name': human_name
        }
        return data_obj
