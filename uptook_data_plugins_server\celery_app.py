from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from django.conf import settings


# 设置Django默认设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'uptook_data_plugins_server.settings')




# 初始化celery
app = Celery('uptook_data_plugins_server')

# 使用Django的设置文件配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动从所有已注册的Django app中加载任务
app.autodiscover_tasks()

from celery.signals import worker_process_init
@worker_process_init.connect
def configure_logging(**kwargs):
    """
    配置Celery worker进程的日志
    避免重复初始化，只在需要时配置
    """
    import logging
    # 检查是否已经配置过日志
    logger = logging.getLogger()
    if not logger.handlers:
        # 只有在没有配置过日志时才初始化
        from public_utils_configs.util.logger import ConcurrentTimedRotatingFileHandler
        ConcurrentTimedRotatingFileHandler.init_logger(level='INFO')

    # 获取当前模块的logger并记录初始化信息
    module_logger = logging.getLogger(__name__)
    module_logger.info("Celery worker process initialized with logging.")