from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult
from _models.company_gz.db_link import LinkPersonEducation
from typing import List, Union


class TBLinekdinPersonEducation:
    db = 'db_link'
    table = 'linekdin_person_education'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql
        self.field_schema = self.db_mysql.read_column_default(self.table, self.db)

    def save_data(self, data: List[LinkPersonEducation], is_safe=True) -> Union[None, MysqlResult]:
        """
        存储更新数据
        """
        if not data:
            return
        if is_safe:
            field_schema = self.field_schema
        else:
            field_schema = None
        datalist = [d.model_dump() for d in data]
        res = self.db_mysql.save(self.table, datalist, db=self.db, field_schema=field_schema)
        return res

    def select_education_data_by_school(self, school_name_list:list):
        """
        通过 领英 链接 ID 查找数据
        """
        if not school_name_list:
            return None
        bfhs = ','.join(['%s'] * len(school_name_list))
        sql = f'''
            select linkedin_id ,sid, school_name  from {self.db}.{self.table} 
            where school_name in ({bfhs}) order by id
        '''

        res = self.db_mysql.execute(sql, value=school_name_list)
        return res

    def select_education_data_by_linkedin_id(self, linkedin_id_list: list):
        """
        通过 领英 链接 ID 查找数据
        """
        if not linkedin_id_list:
            return None
        bfhs = ','.join(['%s'] * len(linkedin_id_list))
        sql = f'''
            select linkedin_id ,sid, school_name  from {self.db}.{self.table} 
            where linkedin_id in ({bfhs}) order by id
        '''

        res = self.db_mysql.execute(sql, value=linkedin_id_list)
        return res
