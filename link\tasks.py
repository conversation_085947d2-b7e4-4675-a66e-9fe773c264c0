from celery import shared_task
from .funcs import funcs_obj
from .route_models import LinkAddReq, LinkResultResp
from .models import LinkTask, LinkResultMap
from django.db import transaction
from settings import logging
from .cos_utils import get_html_from_cos
import traceback

@shared_task()
def process_linkedin_data(link_url: str, cos_path: str, is_zip: int, task_md5: str):
    """
    异步处理LinkedIn数据的Celery任务
    """
    # 添加任务开始日志
    logging.info(f"=== Celery Task Started: process_linkedin_data ===")
    logging.info(f"Task parameters: task_md5={task_md5}, link_url={link_url}, cos_path={cos_path}, is_zip={is_zip}")

    try:
        with transaction.atomic(using='db_link'):
            if LinkTask.objects.using('db_link').filter(task_md5=task_md5, status=2).exists():
                logging.info(f"Task {task_md5} already completed. Skipping.")
                return True

        # 处理数据
        html_str = get_html_from_cos(cos_path)
        result = funcs_obj.deal_req(
            link_url=link_url, html_str=html_str, is_zip=is_zip)
        logging.info(f"Saving result data to database for task: {task_md5}")

        with transaction.atomic(using='db_link'):
            LinkResultMap.objects.using('db_link').create(
                task_md5=task_md5, human_ids=result.human_ids, company_ids=result.company_ids
            )

            # 更新任务状态和结果
            LinkTask.objects.using('db_link').filter(task_md5=task_md5).update(status=2)
            logging.info(f"=== Task {task_md5} process SUCCESS ===")
        return True
    except Exception as e:
        logging.error(f"=== Celery task FAILED: {task_md5} ===")
        logging.error(f"Full traceback:\n{traceback.format_exc()}")

        # 更新任务状态为失败
        try:
            with transaction.atomic(using='db_link'):
                LinkTask.objects.using('db_link').filter(task_md5=task_md5).update(status=3)
            logging.info(f"Task {task_md5} status updated to failed")
        except Exception as db_error:
            logging.error(f"Failed to update task status: {str(db_error)}")

        return False