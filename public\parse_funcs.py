import html


def custom_unescape(text, entities: dict = None):
    if not text:
        return text
    # 先处理标准 HTML 实体
    text = html.unescape(text)
    if not entities:
        # 创建实体替换字典
        entities = {
            '&l;': '<',
            '&lt;': '<',
            '&g;': '>',
            '&gt;': '>',
            '&q;': '"',
            '&ququot;': '"',
            '&quot;': '"',
            '&qquot;': '"',
            '&a;': '&'
            # 添加其他自定义实体
        }
    # 替换实体
    for entity, char in entities.items():
        try:
            text = text.replace(entity, char)
        except Exception as e:
            pass
    return text
