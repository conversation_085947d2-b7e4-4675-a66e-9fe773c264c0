from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult


class TBDepthDataServerInfo:
    db = 'db_source_statistics'
    table = 'depth_data_server_info'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql

    def save_mysql_depth_data_server_info(
            self,
            api_name,
            ip='',
            search_args='',
            results='',
            has_results='',
            hs=0,
            page_no=0,
            page_size=0,
            es_hs=0,
            req_time=0,
            sreq_time=0,
            user_id='',
            total=0
    ):
        """
        存储接口调用情况
        :param api_name: 接口视图名称
        :param ip: 访问ip地址
        :param search_args: 搜索参数
        :param results: 响应结果
        :param has_results: 是否有结果
        :param hs: 接口耗时
        :param page_no: 页码
        :param page_size: 每页大小
        :param es_hs: ES耗时
        :param req_time: 请求开始秒级时间戳
        :param sreq_time: 接收请求开始秒级时间戳
        :param user_id: 用户ID
        :param total: 总数据量
        :return:
        """
        insert_sql = f'''
                insert into {self.db}.{self.table}(`api_name`, `ip`, `search_args`, `results`, `has_results`, 
                `hs`,`page_no`, `page_size`, `es_hs`, `req_time`, 
                `sreq_time`, `user_id`, `total`) 
                values (%(api_name)s, %(ip)s, %(search_args)s, %(results)s, %(has_results)s, 
                %(hs)s, %(page_no)s, %(page_size)s, %(es_hs)s, %(req_time)s, 
                %(sreq_time)s, %(user_id)s, %(total)s)
            '''
        operation = {'api_name': api_name, 'ip': ip, 'search_args': search_args, 'results': results,
                     'has_results': has_results, 'hs': hs, 'page_no': page_no, 'page_size': page_size,
                     'es_hs': es_hs, 'req_time': req_time, 'sreq_time': sreq_time, 'user_id': user_id,
                     'total': total}
        res = self.db_mysql.execute(insert_sql, value=operation)
        return res.rowcount
