from settings import *
from public.public_funcs import PublicFuncs


class LinkFuncs:
    def __init__(self):
        # 国、省、市映射
        self.country_name_map = COUNTRY_NAME_MAP
        self.province_name_map = PROVINCE_NAME_MAP
        self.city_name_map = CITY_NAME_MAP
        self.public_funcs_obj = PublicFuncs()

        # 从数据库读取国省市信息放入 self.area_map
        self.area_map = {}

        self.max_concurrent_uploads = 10

        self.month_dict = {
            "January": 1,
            "Jan": 1,
            "February": 2,
            "Feb": 2,
            "March": 3,
            "Mar": 3,
            "April": 4,
            "Apr": 4,
            "May": 5,
            "June": 6,
            "Jun": 6,
            "July": 7,
            "Jul": 7,
            "August": 8,
            "Aug": 8,
            "September": 9,
            "Sept": 9,
            "Sep": 9,
            "October": 10,
            "Oct": 10,
            "November": 11,
            "Nov": 11,
            "December": 12,
            "Dec": 12
        }

    def get_date_time(self, date_str: str,language='cn'):

        date_obj = dict()
        cut_index = date_str.find('·')
        date_str = date_str[:cut_index].strip() if cut_index != -1 else date_str.strip()
        date_list = [date.strip() for date in date_str.split('-') if date.strip()]
        if len(date_list) == 2:
            if language == 'en':
                for date_data_index in range(len(date_list)):
                    try:
                        if date_data_index == 1 and date_list[date_data_index] == 'Present':
                            date_obj['end_date'] = ''
                            break
                        date_data_list = [d for d in date_list[date_data_index].split(' ') if d]
                        if len(date_data_list) == 1:
                            if date_data_index == 0:
                                date_obj['start_date'] = date_data_list[0]
                            elif date_data_index == 1:
                                date_obj['end_date'] = date_data_list[0]
                        elif len(date_data_list) == 2:
                            if date_data_index == 0:
                                date_obj['start_date'] = date_data_list[1] + '-' + '{:0>2d}'.format(self.month_dict.get(date_data_list[0])) if date_data_list[1] else ''
                            elif date_data_index == 1:
                                date_obj['end_date'] = date_data_list[1] + '-' + '{:0>2d}'.format(self.month_dict.get(date_data_list[0])) if date_data_list[1] else ''
                    except Exception as e:
                        logging.error(f'{e} === {date_list}:{date_data_index}')
                        pass
            elif language == 'cn':
                for date_data_index in range(len(date_list)):
                    try:
                        if date_data_index == 1 and date_list[date_data_index] == '至今':
                            date_obj['end_date'] = ''
                            break
                        date_str = ''
                        year_index = date_list[date_data_index].find('年')
                        month_index = date_list[date_data_index].find('月')
                        day_index = date_list[date_data_index].find('日')
                        if year_index <0:
                            date_obj['end_date'] = ''
                            continue
                        if not f'{date_list[date_data_index][:year_index]}'.isdigit():
                            continue
                        date_str += f'{date_list[date_data_index][:year_index]}'

                        if month_index <0:
                            if date_data_index == 0:
                                date_obj['start_date'] = date_str
                            else:
                                date_obj['end_date'] = date_str
                            continue

                        if not f'{date_list[date_data_index][year_index+1:month_index]}'.isdigit():
                            continue
                        date_str += '-'+'{:0>2d}'.format(eval(f'{date_list[date_data_index][year_index+1:month_index]}'))

                        if day_index <0:
                            if date_data_index == 0:
                                date_obj['start_date'] = date_str
                            else:
                                date_obj['end_date'] = date_str
                            continue
                        if not f'{date_list[date_data_index][month_index+1:day_index]}'.isdigit():
                            continue
                        date_str += '-' + '{:0>2d}'.format(eval(f'{date_list[date_data_index][month_index+1:day_index]}'))
                        if date_data_index == 0:
                            date_obj['start_date'] = date_str
                        else:
                            date_obj['end_date'] = date_str
                    except Exception as e:
                        logging.error(f'{e} === {date_list}:{date_data_index}')
        return date_obj

    def mate_area_data(self, region: str):
        """
        匹配 国省市
        """
        if not region:
            return {}
        area_list = [area.strip() for area in region.split(',')]
        area_data_info = {}
        country_code = ''
        province_en = ''
        # 先匹配国家
        country_idx = None
        for idx, area in enumerate(area_list):
            area = self.country_name_map.get(area.lower(), '') or area
            area_data_info = self.public_funcs_obj.mate_area_data(
                self.area_map,
                country=area
            )
            country_iso_code = area_data_info.get('country_iso_code', '')
            if country_iso_code:
                country_code = country_iso_code
                country_idx = idx
                break
        # 剔除已匹配到国家的索引位数据
        if country_idx is not None:
            del area_list[country_idx]
        # 匹配省份
        province_idx = None
        for idx, area in enumerate(area_list):
            area = self.province_name_map.get(area.lower(), '') or area
            area_data_info = self.public_funcs_obj.mate_area_data(
                self.area_map,
                country_code=country_code,
                province=area
            )
            province_id = area_data_info.get('province_id', 0)
            if province_id:
                province_en = area_data_info.get('province_en', '')
                province_idx = idx
                break
        # 剔除已匹配到国家的索引位数据
        if province_idx is not None:
            del area_list[province_idx]
        # 匹配城市
        for idx, area in enumerate(area_list):
            area = self.city_name_map.get(area.lower(), '') or area
            area_data_info = self.public_funcs_obj.mate_area_data(
                self.area_map,
                country_code=country_code,
                province=province_en,
                city=area,
            )
            city_id = area_data_info.get('city_id', 0)
            if city_id:
                break
        country_iso_code = area_data_info.get('country_iso_code', '')
        if not country_iso_code:
            logging.warning(f'该地址未匹配出标准国省市: {region}')
            return {}
        return area_data_info


if __name__ == '__main__':
    l = LinkFuncs()
    time_str = '2023年5月6日 - 2023年9月16日 · 10 个月'
    print(l.get_date_time(time_str))