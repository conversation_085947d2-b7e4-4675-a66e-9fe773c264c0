
from svenv import VENV
from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql
from public_utils_configs.config.config_mysql_pool import MysqlConfig

max_connections = 3
if VENV == 'test':
    uptook_tdsql_comp_mysql_obj = DBPoolMysql(
        MysqlConfig.uptook_tdsql_comp_ww_mysql_config,
        max_connections=max_connections
    )
else:
    uptook_tdsql_comp_mysql_obj = DBPoolMysql(
        MysqlConfig.uptook_tdsql_comp_mysql_config,
        max_connections=max_connections
    )

