# 路由 模型
from pydantic import BaseModel, Field
from typing import List, Optional, Union, Dict


class LinkParseReq(BaseModel):
    """
    link 页面解析 接口 请求参数 模型
    """
    link_url: str   # 页面链接
    html_str: str   # 页面内容
    is_zip: int = 0   # 是否压缩;默认不压缩



class LinkAddReq(BaseModel):
    """
    link 页面解析 接口 请求参数 模型
    """
    link_url: str   # 页面链接
    html_str: str   # 页面内容
    is_zip: int = 0   # 是否压缩;默认不压缩


class LinkAddResp(BaseModel):
    """
    link 添加 接口 响应结果 模型
    """
    task_id: str = Field(default='')       # 任务 ID


class LinkBatchReq(BaseModel):
    """
    link 任务状态、结果 接口 请求参数 模型
    """
    task_id: str = Field(default='')       # 任务 ID

class LinkResultResp(BaseModel):
    """
    link 页面解析 接口 响应结果 模型
    """
    human_ids: List[str] = Field(default=[])       # 人物 ID列表
    company_ids: List[str] = Field(default=[])      # 公司 ID列表

