from pydantic import BaseModel, Field, constr, conint, model_validator
from typing import List, Optional, Union


class ParseSearchAllPeopleFollowee(BaseModel):
    """
    搜索综合页 人物 关注情况视图 解析模型
    """
    linkedin_id: Optional[str] = Field(max_length=50)  # 领英人物 ID
    follower_count: Optional[conint(ge=0)] = Field(default=0)  # 领英人物 关注者数量


class ParseSearchAllCompanyFollowee(BaseModel):
    """
    搜索综合页 公司 关注情况视图 解析模型
    """
    job_company_linkedin_id: Optional[str] = Field(max_length=50)  # 领英公司 ID
    follower_count: Optional[conint(ge=0)] = Field(default=0)  # 领英人物 关注者数量


class ParseSearchAllPeople(BaseModel):
    """
    搜索综合页 人物 解析模型
    """
    linkedin_id: Optional[str] = Field(default='', max_length=50)  # 领英人物 ID
    linkedin_username: Optional[str] = Field(max_length=100)  # 领英人物 链接ID
    linkedin_url: Optional[str] = Field(max_length=500)  # 领英人物 链接
    full_name: Optional[str] = Field(max_length=300)  # 领英人物 名称
    person_logos: Optional[str] = Field(default='')  # 领英人物 头像列表json
    person_profiles: Optional[str] = Field(default='')  # 领英人物 简介
    region: Optional[str] = Field(default='', max_length=300)   # 领英人物 地区
    lid: constr(max_length=30) = ''  # 领英人物ID


class ParseSearchAllCompany(BaseModel):
    """
    搜索综合页 公司 解析模型
    """
    job_company_id: Optional[str] = Field(max_length=100)  # 领英公司 链接ID
    job_company_linkedin_id: Optional[str] = Field(default='', max_length=50)  # 领英公司 ID
    job_company_linkedin_url: Optional[str] = Field(default='', max_length=500)  # 领英公司 链接
    job_company_name: Optional[str] = Field(default='', max_length=300)  # 领英公司 名称
    company_logos: Optional[str] = Field(default='')  # 领英公司 头像列表json
    company_profiles: Optional[str] = Field(default='')  # 领英公司 简介
    job_search_url: Optional[str] = Field(default='', max_length=500)  # 领英公司 职位搜索链接
    job_num: Optional[str] = Field(default='', max_length=10)  # 领英公司 职位数量
    followers: Optional[str] = Field(default='', max_length=10)  # 领英公司 关注者数量
    industry: Optional[str] = Field(default='', max_length=100)  # 领英公司 行业
    region: Optional[str] = Field(default='', max_length=300)  # 领英公司 地区


class ParseSearchAllPost(BaseModel):
    """
    搜索综合页 帖子 解析模型
    """
    post_id: Optional[str] = Field(max_length=50)  # 领英帖子 ID
    people_model: Union[ParseSearchAllPeople, None] = Field(default=None)
    company_model: Union[ParseSearchAllCompany, None] = Field(default=None)
    post_title: Optional[str] = Field(default='', max_length=500)  # 帖子 标题
    post_subtitle: Optional[str] = Field(default='', max_length=500)  # 帖子 子标题
    post_share_url: Optional[str] = Field(default='', max_length=1000)  # 帖子 链接
    post_images: Optional[str] = Field(default='')  # 帖子 图片列表json
    post_action_target: Optional[str] = Field(default='', max_length=1000)  # 帖子 内容链接
    post_accessibility_text: Optional[str] = Field(default='')  # 帖子 内容

    @model_validator(mode='before')
    def check_required_fields(cls, values):
        people_model = values.get('people_model', None)
        company_model = values.get('company_model', None)

        if not (people_model or company_model):
            raise ValueError("At least one of 'people_model', or 'company_model' must be provided.")
        return values


class ParseSearchAllPostCount(BaseModel):
    """
    搜索综合页 帖子 统计视图 解析模型
    """
    post_id: Optional[str] = Field(max_length=50)
    like_num: Optional[conint(ge=0)] = Field(default=0)  # 帖子 点赞数量
    comments_num: Optional[conint(ge=0)] = Field(default=0)  # 帖子 评论数量
    shares_num: Optional[conint(ge=0)] = Field(default=0)  # 帖子 转发分享数量

