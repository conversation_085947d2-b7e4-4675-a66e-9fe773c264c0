from django.db import models


class LinkTask(models.Model):
    """
    领英 任务 模型
    """
    id = models.AutoField(primary_key=True, db_column='id')
    task_md5 = models.CharField(max_length=64, unique=True)
    cos_path = models.Char<PERSON>ield(max_length=255)
    is_zip = models.BooleanField(default=False)
    link_url = models.CharField(max_length=255)
    status = models.IntegerField(default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'linkedin_link_task'
        managed = False


class LinkResultMap(models.Model):
    """
    领英任务ID 结果 映射
    """
    id = models.AutoField(primary_key=True, db_column='id')
    task_md5 = models.Char<PERSON>ield(max_length=64, unique=True)
    human_ids = models.Char<PERSON>ield(max_length=255)
    company_ids = models.Char<PERSON>ield(max_length=255)
    create_time = models.DateTime<PERSON>ield(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'linkedin_link_result_map'
        managed = False