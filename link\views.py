import time
import sys
import json
import traceback
from django.shortcuts import render
from django.views.decorators.http import require_http_methods

from public_funcs import JsonError, JsonResponse, get_access_ip
from settings import *
from link.route_models import *
from link.funcs import funcs_obj
from public_utils_configs.util.mysql_pool_v3 import MysqlResult
from link.route_models import *
import gc


@require_http_methods(['POST'])
def link_parse(request):
    """
    link 页面解析 接口
    """
    try:
        access_ip = get_access_ip(request)
        api_name = sys._getframe().f_code.co_name
        vs_time = time.time()
        headers = request.META
        # logging.debug(headers)
        user_id = headers.get('HTTP_USERID', '')
        req_time = headers.get('HTTP_STARTTIME', '')
        req_time = int(req_time) if req_time else 0

        # 解析请求数据
        req = LinkParseReq.model_validate_json(request.body)
        res = funcs_obj.deal_req(link_url=req.link_url, html_str=req.html_str, is_zip=req.is_zip)
        data = res.model_dump()
        return JsonResponse(data, msg='success', code=200)
    except Exception as e:
        logging.error(f'{traceback.format_exc()}')
        return JsonError('Interface exception')
    
    finally:
        del res, data
        gc.collect()



@require_http_methods(['POST'])
def link_add(request):
    """
    link 添加任务 接口
    """
    try:
        access_ip = get_access_ip(request)
        api_name = sys._getframe().f_code.co_name
        vs_time = time.time()
        headers = request.META
        # logging.debug(headers)
        user_id = headers.get('HTTP_USERID', '')
        req_time = headers.get('HTTP_STARTTIME', '')
        req_time = int(req_time) if req_time else 0

        # 解析请求数据
        req = LinkAddReq.model_validate_json(request.body)
        res = funcs_obj.process_task(req)
        data = res.model_dump()
        return JsonResponse(task_status='SUCCESS', rs_data=data, msg='success', code=200)
    except Exception as e:
        logging.error(f'{traceback.format_exc()}')
        return JsonError('Interface exception')
    

@require_http_methods(['POST'])
def link_info(request):
    """
    link 任务结果 接口
    """
    try:
        req = LinkBatchReq.model_validate_json(request.body)
        res = funcs_obj.check_task(req=req)
        if "SUCCESS" in res.values():
            info = funcs_obj.get_task_result(req=req)
            return JsonResponse(task_status='SUCCESS', rs_data=info, msg='success', code=200)
        else:
            first_task_status = next(iter(res.values())) 
            return JsonResponse(task_status=first_task_status, msg='success', code=200)
    except Exception as e:
        logging.error(f'{traceback.format_exc()}')
        return JsonError('Interface exception')
    
    finally:
        del res
        gc.collect()


@require_http_methods(['GET'])
def link_status(request):
    """
    link 任务状态 接口
    """
    try:
        req = LinkBatchReq.model_validate_json(request.body)
        res = funcs_obj.check_task(req=req)
        data = res.model_dump()
        return JsonResponse(data, msg='success', code=200)
    except Exception as e:
        logging.error(f'{traceback.format_exc()}')
        return JsonError('Interface exception')
    

@require_http_methods(["POST"])
def link_data(request):
    req = LinkBatchReq.model_validate_json(request.body)
    