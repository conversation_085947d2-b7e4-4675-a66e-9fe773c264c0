from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult


class TBStdAreaCity:
    db = 'db_std_map'
    table = 'std_area_city'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql

    def get_city_map(self):
        sql = f'''SELECT id, code_iso2, name_en, name_local,name_cn,province_id FROM {self.db}.{self.table}'''
        data_list = self.db_mysql.execute(sql).data
        city_dict = dict()
        for one in data_list:
            if one["province_id"] not in city_dict:
                city_dict[one["province_id"]] = [
                    {
                        "code_iso2": one["code_iso2"],
                        "name_en": one["name_en"],
                        "name_cn": one["name_cn"],
                        "name_local": one["name_local"],
                    }
                ]
            else:
                city_dict[one["province_id"]].append({
                        "code_iso2": one["code_iso2"],
                        "name_en": one["name_en"],
                        "name_cn": one["name_cn"],
                        "name_local": one["name_local"],
                })
        return city_dict
