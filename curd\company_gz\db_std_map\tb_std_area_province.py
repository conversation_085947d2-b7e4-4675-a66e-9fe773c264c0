from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult


class TBStdAreaProvince:
    db = 'db_std_map'
    table = 'std_area_province'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql

    def get_province_map(self):
        sql = f'''SELECT id, code_iso2, name_en, name_local,name_cn,province_code FROM {self.db}.{self.table}'''
        data_list = self.db_mysql.execute(sql).data
        jwd_province_map = dict()
        province_country_map = dict()
        for one in data_list:
            province_country_map[one["name_en"].lower()] = {
                    "province_id": one["id"],
                    "code_iso2": one["code_iso2"],
                    "name_en": one["name_en"],
                    "name_cn": one["name_cn"],
                    "name_local": one["name_local"],
                }
            province_country_map[one["name_cn"].lower()] = {
                    "province_id": one["id"],
                    "code_iso2": one["code_iso2"],
                    "name_en": one["name_en"],
                    "name_cn": one["name_cn"],
                    "name_local": one["name_local"],
                }
            province_country_map[one["name_local"].lower()] = {
                    "province_id": one["id"],
                    "code_iso2": one["code_iso2"],
                    "name_en": one["name_en"],
                    "name_cn": one["name_cn"],
                    "name_local": one["name_local"],
                }
        return province_country_map
