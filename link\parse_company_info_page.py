import json
import re
from lxml import etree
from public.parse_funcs import custom_unescape


class NotLoginCompanyParse:

    def __init__(self):
        self.json_data = './/script[@type="application/ld+json"]//text()'
        self.overview_xpath = './/dl[@class="mt-6"]/div'
        self.jod_url_xpath = './/a[@data-tracking-control-name="nav_type_jobs"]/@href'
        self.human_xpath = './/section[@data-test-id="employees-at"]/div[@class="core-section-container__content break-words"]/ul/li'
        self.replace_dict = {
            '\n': '',
            '\t;': '<',
        }

    def parse_data(self, html_str):
        html = etree.HTML(html_str)
        org_data = html.xpath(self.json_data)
        try:
            org_data = json.loads(org_data[0])
            main_data = org_data.get('@graph', [])[-1]
            address_data = org_data.get('@graph', [])[9].get('address', {})
            json_data = {**main_data, **address_data}
            
        except:
            json_data = dict()
        items = html.xpath(self.overview_xpath)
        overview = dict()
        for x in range(len(items)):
            key_xpath = f'{self.overview_xpath}[{x + 1}]/dt[1]//text()'
            value_xpath = f'{self.overview_xpath}[{x + 1}]/dd[1]//text()'
            key = html.xpath(key_xpath)
            key = custom_unescape(key[0], entities=self.replace_dict).strip() if key else ''
            value = html.xpath(value_xpath)
            value = custom_unescape(value[0], entities=self.replace_dict).strip() if value else ''
            if not key or not value:
                continue
            overview[key] = value
        json_data['overview'] = overview
        jod_url = html.xpath(self.jod_url_xpath)
        jod_url = jod_url[0] if jod_url else ''
        json_data['jod_url'] = jod_url
        human_list = []
        human_items = html.xpath(self.human_xpath)
        for human_index in range(len(human_items)):
            human_full_name_xpath = f'{self.human_xpath}[{human_index + 1}]/a/div/h3//text()'
            human_url_xpath = f'{self.human_xpath}[{human_index + 1}]/a/@href'
            job_title_xpath = f'{self.human_xpath}[{human_index + 1}]/a/div/h4//text()'
            human_full_name = html.xpath(human_full_name_xpath)
            human_url = html.xpath(human_url_xpath)
            job_title = html.xpath(job_title_xpath)
            if not human_url or not human_full_name:
                continue
            human_url = custom_unescape(human_url[0], entities=self.replace_dict).strip()
            human_id = re.findall('linkedin\.com/in/(.*)', human_url)
            if not human_id:
                continue
            cut_index = human_id[0].find('?')
            human_id = human_id[0][:cut_index] if cut_index >= 0 else human_id
            if not human_id:
                continue
            human_full_name = custom_unescape(human_full_name[0], entities=self.replace_dict).strip()
            job_title = custom_unescape(job_title[0], entities=self.replace_dict).strip() if job_title else ''
            human_obj = {
                'full_name': human_full_name,
                'linkedin_username': human_id,
                'job_title': job_title
            }
            human_list.append(human_obj)
        json_data['human_list'] = human_list

        return json_data


class LoginCompanyParse:

    def __init__(self):
        self.json_data_xpath = './/code[contains(@id,"bpr-guid-") and not(contains(@id,"datalet"))]//text()'
        self.company_name_xpath = './/a[@aria-current="page"]/@href'
        self.company_name_pat = re.compile(r'/company/(.*?)/')

    def parse_data(self, html_str):
        html_obj = etree.HTML(html_str)

        company_name_obj = html_obj.xpath(self.company_name_xpath)
        company_name = company_name_obj[0] if company_name_obj else ''
        company_name = self.company_name_pat.findall(company_name)
        data_obj = {
            'html_str': html_str,
            'company_name': company_name[0] if company_name else ''
        }

        return data_obj
