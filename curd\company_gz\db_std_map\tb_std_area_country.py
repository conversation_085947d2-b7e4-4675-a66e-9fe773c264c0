from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult


class TBStdAreaCountry:
    db = 'db_std_map'
    table = 'std_area_country'

    def __init__(self, db_mysql: DBPoolMysql):
        self.db_mysql: DBPoolMysql = db_mysql

    def mapping_country(self):
        # 读取c_area_country到内存
        sql = f"select id, name_cn, name_en, name_local, code_iso2 from {self.db}.{self.table}"
        data_raw = self.db_mysql.execute(sql)
        mapping = dict()
        for data in data_raw.data:
            _id, name_cn, name_en, name_local, code_iso2 = data['id'], data['name_cn'], data['name_en'], data['name_local'], data['code_iso2']
            standard = {"id": _id, "name_en": name_en, 'code_iso2': code_iso2}
            mapping[name_cn.lower()] = standard
            mapping[name_en.lower()] = standard
            mapping[name_local.lower()] = standard
            mapping[code_iso2.lower()] = standard
        return mapping
