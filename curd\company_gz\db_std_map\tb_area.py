#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_area.py
@Date: 2024/1/5
@Desc: 获取地区相关数据
@Server: 
"""
from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, MysqlResult
from typing import Sequence


class TableArea:
    db = 'db_std_map'
    country_table = 'std_area_country'
    province_table = 'std_area_province'
    city_table = 'std_area_city'

    def __init__(self, db_mysql):
        self.db_mysql: DBPoolMysql = db_mysql

    def get_country_code_map(self) -> dict:
        """
        获取 国家 编码
        :return:
        """
        sql = f'''
            select `name_en` as country, `code_iso2` as country_iso_code, `name_cn` as country_cn,
            `name_local` as country_local, `code_iso3` as country_iso_code3, `code_iso_num` as code_iso_num, `code_idd` as code_idd
             from {self.db}.{self.country_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        country_code_map = {}
        for data in results:
            country = data.get('country', '')
            country_iso_code = data.get('country_iso_code', '')
            country_cn = data.get('country_cn', '')
            country_local = data.get('country_local', '')
            country_iso_code3 = data.get('country_iso_code3', '')
            code_iso_num = data.get('code_iso_num', '')
            code_idd = data.get('code_idd', '')
            country_code_map[country_iso_code] = {
                'country_en': country, 'country_cn': country_cn,
                'country_local': country_local,
                'country_iso_code3': country_iso_code3,
                'code_iso_num': code_iso_num, 'code_idd': code_idd}
        return country_code_map

    def get_province_id_map(self) -> dict:
        """
        获取 省份ID 映射
        :return:
        """
        sql = f'''
            select `name_cn` as province_cn, `name_en` as province_en, `id` as province_id, `province_code` as province_code from {self.db}.{self.province_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        code_map = {}
        for data in results:
            province_id = data.get('province_id', 0)
            province_en = data.get('province_en', '')
            province_cn = data.get('province_cn', '')
            province_code = data.get('province_code', '')
            code_map[province_id] = {'province_en': province_en, 'province_cn': province_cn,
                                     'province_code': province_code}
        return code_map

    def get_city_id_map(self) -> dict:
        """
        获取 城市ID 映射
        :return:
        """
        sql = f'''
            select `name_cn` as city_cn, `name_en` as city_en, `id` as city_id, `name_local` as city_local from {self.db}.{self.city_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        code_map = {}
        for data in results:
            city_id = data.get('city_id', 0)
            city_cn = data.get('city_cn', '')
            city_en = data.get('city_en', '')
            city_local = data.get('city_local', '')
            code_map[city_id] = {'city_en': city_en, 'city_cn': city_cn, 'city_local': city_local}
        return code_map

    def get_area_map(self) -> dict:
        country_map = self.get_country_map()
        province_map = self.get_province_map()
        city_map = self.get_city_map()
        area_map = {'country_map': country_map, 'province_map': province_map, 'city_map': city_map}
        return area_map

    def get_country_map(self) -> dict:
        """
        获取 国家 编码
        :return:
        """
        sql = f"select id, `name_en` en, `code_iso2` `code`, `code_iso3` `code3`, `name_cn` cn from {self.db}.{self.country_table}"
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        country_map = dict()
        for data in results:
            en = data.get('en', '').strip()
            code = data.get('code', '').strip()
            cn = data.get('cn', '').strip()
            code3 = data.get('code3', '').strip()
            aid = data.get('id', 0)
            if code:
                country_map[code.lower()] = {'id': aid, 'code': code, 'en': en, 'cn': cn}
            if code3:
                country_map[code3.lower()] = {'id': aid, 'code': code, 'en': en, 'cn': cn}
            if en:
                country_map[en.lower()] = {'id': aid, 'code': code, 'en': en, 'cn': cn}
            if cn:
                country_map[cn.lower()] = {'id': aid, 'code': code, 'en': en, 'cn': cn}
        return country_map

    def get_province_map(self) -> dict:
        """
        获取 省份ID 映射
        :return:
        """
        sql = (f"select t1.`id` id, t1.`name_cn` cn, t1.`name_en` en, t2.`code_iso2` `code`, t1.`province_code` `province_code`"
               f"from {self.db}.{self.province_table} t1 "
               f"left join {self.db}.{self.country_table} t2 on t1.code_iso2=t2.code_iso2")
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        province_map = dict()
        for data in results:
            try:
                _id = data.get('id', 0)
                en = data.get('en', '').strip()
                cn = data.get('cn', '').strip()
                code = data.get('code', '').strip()
                province_code = data.get('province_code', '').strip()
                province_map[_id] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
                if en:
                    province_map[en.lower()] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
                if cn:
                    province_map[cn.lower()] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
                if province_code:
                    province_map[province_code.lower()] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
            except Exception as e:
                continue
        return province_map

    def get_city_map(self) -> dict:
        """
        获取 城市ID 映射
        :return:
        """
        sql = (
            f"select t1.`id` id, t1.`name_cn` cn, t1.`name_en` en, t1.`province_id` province_id, t3.`code_iso2` `code` "
            f"from {self.db}.{self.city_table} t1 "
            f"left join {self.db}.{self.country_table} t3 on t1.code_iso2=t3.code_iso2")
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        city_map = {}
        for data in results:
            _id = data.get('id', 0)
            cn = data.get('cn', '').strip()
            en = data.get('en', '').strip()
            province_id = data.get('province_id', 0)
            code = data.get('code', '').strip()
            if en:
                city_map[en.lower()] = {'id': _id, 'en': en, 'cn': cn, 'province_id': province_id, 'code': code}
            if cn:
                city_map[cn.lower()] = {'id': _id, 'en': en, 'cn': cn, 'province_id': province_id, 'code': code}
        return city_map
